package entity;

import core.Pos;
import core.Renderable;
import tileengine.TETile;
import ui.themes.Theme;
import ui.themes.ThemeManager;

public abstract class Entity implements Renderable {

    public enum EntityType {
        PLAYER, GHOST, PELLET, POWER_PELLET
    }

    public EntityType type;
    public String name;
    public Pos position;

    public TETile[] tiles; // Entity can have multiple tiles for animation or different states

    public Entity(EntityType type, String name, TETile tile) {
        this.type = type;
        this.name = name;
        this.position = new Pos(0, 0); // Initialize at origin
        this.tiles = new TETile[]{tile}; // Initialize tiles array with single tile
        
        // Register for theme updates
        ThemeManager.getInstance().addThemeListener(this);
    }

    public Entity(EntityType type, String name, TETile[] tiles) {
        this.type = type;
        this.name = name;
        this.position = new Pos(0, 0); // Initialize at origin
        this.tiles = tiles;
        
        // Register for theme updates
        ThemeManager.getInstance().addThemeListener(this);
    }
    
    public Pos getPosition() {
        return position;
    }
    
    public void setPosition(Pos newPos) {
        this.position = newPos;
    }
    
    public void setPosition(int x, int y) {
        this.position.x = x;
        this.position.y = y;
    }
    
    /**
     * Render this entity to its current position.
     * Default implementation renders the current tile at the entity's position.
     * Subclasses can override for custom rendering behavior.
     */
    @Override
    public void render() {
        if (position != null && getCurrentTile() != null) {
            getCurrentTile().draw(position.x, position.y);
        }
    }
    
    /**
     * Get the current tile for this entity.
     * Default implementation returns the first tile.
     * Subclasses should override for animation or state-based tiles.
     */
    public TETile getCurrentTile() {
        return (tiles != null && tiles.length > 0) ? tiles[0] : null;
    }
    
    /**
     * Called when theme changes. Integrates with existing updateTheme method.
     * Subclasses should override updateTheme() to refresh their themed sprites.
     */
    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        updateTheme();
    }
    
    /**
     * Update entity appearance when theme changes.
     * Subclasses should override this to refresh their themed sprites.
     */
    public abstract void updateTheme();
}
