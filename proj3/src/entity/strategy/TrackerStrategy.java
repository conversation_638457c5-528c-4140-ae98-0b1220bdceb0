package entity.strategy;

import core.Pos;
import entity.Player;
import tileengine.TETile;
import ui.SpriteManager.Direction;
import utils.AStar;
import utils.Log;
import java.util.ArrayList;
import java.util.List;

public class TrackerStrategy implements GhostStrategy {
    private TETile[][] world;
    private Player targetPlayer;
    private List<Pos> currentPath;
    private int currentPathIndex;
    private double moveTimer;
    private double moveSpeed; // Time between moves in seconds
    private double pathRefreshTimer;
    private double pathRefreshInterval; // Time between path recalculations in seconds
    private Pos lastPlayerPosition; // To detect if player moved
    
    public TrackerStrategy(TETile[][] world, Player targetPlayer, double moveSpeed, double pathRefreshInterval) {
        this.world = world;
        this.targetPlayer = targetPlayer;
        this.moveSpeed = moveSpeed;
        this.pathRefreshInterval = pathRefreshInterval;
        this.currentPath = new ArrayList<>();
        this.currentPathIndex = 0;
        this.moveTimer = 0.0;
        this.pathRefreshTimer = 0.0;
        this.lastPlayerPosition = null;
    }
    
    @Override
    public Direction getNextMove(Pos currentPos, double deltaTime) {
        if (targetPlayer == null || targetPlayer.getPosition() == null) {
            return null;
        }
        
        moveTimer += deltaTime;
        if (moveTimer < moveSpeed) {
            return null; // Not time to move yet
        }
        
        // Check if we need to recalculate the path
        Pos playerPos = targetPlayer.getPosition();
        if (shouldRecalculatePath(currentPos, playerPos, deltaTime)) {
            recalculatePath(currentPos, playerPos);
        }
        
        // Follow the current path
        if (currentPath.isEmpty() || currentPathIndex >= currentPath.size()) {
            return null; // No path or reached end of path
        }
        
        Pos nextTarget = currentPath.get(currentPathIndex);
        
        // Check if we've reached the current target
        if (currentPos.x == nextTarget.x && currentPos.y == nextTarget.y) {
            currentPathIndex++;
            if (currentPathIndex >= currentPath.size()) {
                return null; // Reached end of path
            }
            nextTarget = currentPath.get(currentPathIndex);
        }
        
        // Calculate direction to next target
        Direction direction = calculateDirection(currentPos, nextTarget);
        
        if (direction != null) {
            moveTimer = 0.0; // Reset timer after successful move
        }
        
        return direction;
    }
    
    /**
     * Determine if we should recalculate the path
     */
    private boolean shouldRecalculatePath(Pos ghostPos, Pos playerPos, double deltaTime) {
        pathRefreshTimer += deltaTime;
        
        // Recalculate if enough time has passed
        if (pathRefreshTimer >= pathRefreshInterval) {
            return true;
        }
        
        // Recalculate if player moved significantly from last known position
        if (lastPlayerPosition != null) {
            double distanceMoved = Math.abs(playerPos.x - lastPlayerPosition.x) + 
                                 Math.abs(playerPos.y - lastPlayerPosition.y);
            if (distanceMoved >= 3) { // Player moved 3+ tiles
                return true;
            }
        }
        
        // Recalculate if we don't have a path
        if (currentPath.isEmpty()) {
            return true;
        }
        
        // Recalculate if we've reached the end of our current path
        if (currentPathIndex >= currentPath.size()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Recalculate path using A* algorithm
     */
    private void recalculatePath(Pos ghostPos, Pos playerPos) {
        pathRefreshTimer = 0.0;
        lastPlayerPosition = new Pos(playerPos.x, playerPos.y);
        
        List<Pos> newPath = AStar.findPath(world, ghostPos, playerPos);
        
        if (!newPath.isEmpty()) {
            currentPath = newPath;
            currentPathIndex = 0;
            Log.debug("RedGhost recalculated path with " + newPath.size() + " steps to player at (" + 
                     playerPos.x + ", " + playerPos.y + ")");
        } else {
            // No path found, keep current path or clear it
            Log.debug("RedGhost: No path found to player at (" + playerPos.x + ", " + playerPos.y + ")");
            if (currentPathIndex >= currentPath.size()) {
                currentPath.clear(); // Clear path if we were at the end anyway
            }
        }
    }
    
    /**
     * Calculate direction from current position to target
     */
    private Direction calculateDirection(Pos from, Pos to) {
        int dx = Integer.compare(to.x, from.x);
        int dy = Integer.compare(to.y, from.y);
        
        // Prioritize one axis movement (move horizontally first, then vertically)
        if (dx != 0) {
            return dx > 0 ? Direction.RIGHT : Direction.LEFT;
        } else if (dy != 0) {
            return dy > 0 ? Direction.UP : Direction.DOWN;
        }
        
        return null; // Already at target
    }
    
    @Override
    public List<Pos> getMovingPath() {
        // Return the current calculated path for visualization
        return new ArrayList<>(currentPath);
    }
    
    @Override
    public void reset() {
        currentPath.clear();
        currentPathIndex = 0;
        moveTimer = 0.0;
        pathRefreshTimer = 0.0;
        lastPlayerPosition = null;
    }
    
    @Override
    public void update(double deltaTime) {
        // Strategy state is updated in getNextMove
    }
    
    /**
     * Get the current path refresh interval
     */
    public double getPathRefreshInterval() {
        return pathRefreshInterval;
    }
    
    /**
     * Set the path refresh interval
     */
    public void setPathRefreshInterval(double interval) {
        this.pathRefreshInterval = interval;
    }
    
    /**
     * Get the current path index for debugging
     */
    public int getCurrentPathIndex() {
        return currentPathIndex;
    }
    
    /**
     * Check if the ghost has a valid path
     */
    public boolean hasValidPath() {
        return !currentPath.isEmpty() && currentPathIndex < currentPath.size();
    }
}