package entity.strategy;

import core.Pos;
import ui.SpriteManager.Direction;
import java.util.List;

public interface GhostStrategy {
    /**
     * Get the next move direction for the ghost based on current position
     * @param currentPos Current position of the ghost
     * @param deltaTime Time elapsed since last update
     * @return Direction to move next, or null if no movement
     */
    Direction getNextMove(Pos currentPos, double deltaTime);
    
    /**
     * Get the complete moving path for visualization
     * @return List of positions representing the ghost's movement path
     */
    List<Pos> getMovingPath();
    
    /**
     * Reset the strategy to its initial state
     */
    void reset();
    
    /**
     * Update strategy state (called each frame)
     * @param deltaTime Time elapsed since last update
     */
    void update(double deltaTime);
}