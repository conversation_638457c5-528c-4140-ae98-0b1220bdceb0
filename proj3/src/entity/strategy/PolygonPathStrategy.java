package entity.strategy;

import core.Pos;
import ui.SpriteManager.Direction;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PolygonPathStrategy implements GhostStrategy {
    private List<Pos> pathPoints;
    private int currentTargetIndex;
    private double moveTimer;
    private double moveSpeed; // Time between moves in seconds
    
    public PolygonPathStrategy(List<Pos> pathPoints, double moveSpeed) {
        this.pathPoints = new ArrayList<>(pathPoints);
        this.currentTargetIndex = 0;
        this.moveTimer = 0.0;
        this.moveSpeed = moveSpeed;
    }
    
    public PolygonPathStrategy(Pos[] pathPoints, double moveSpeed) {
        this(Arrays.asList(pathPoints), moveSpeed);
    }
    
    @Override
    public Direction getNextMove(Pos currentPos, double deltaTime) {
        if (pathPoints.isEmpty()) return null;
        
        moveTimer += deltaTime;
        if (moveTimer < moveSpeed) {
            return null; // Not time to move yet
        }
        
        // Get current target position
        Pos target = pathPoints.get(currentTargetIndex);
        
        // Check if we've reached the current target
        if (currentPos.x == target.x && currentPos.y == target.y) {
            // Move to next target
            currentTargetIndex = (currentTargetIndex + 1) % pathPoints.size();
            target = pathPoints.get(currentTargetIndex);
        }
        
        // Calculate direction to target
        Direction direction = calculateDirection(currentPos, target);
        
        if (direction != null) {
            moveTimer = 0.0; // Reset timer after successful move
        }
        
        return direction;
    }
    
    private Direction calculateDirection(Pos from, Pos to) {
        int dx = Integer.compare(to.x, from.x);
        int dy = Integer.compare(to.y, from.y);
        
        // Prioritize one axis movement (move horizontally first, then vertically)
        if (dx != 0) {
            return dx > 0 ? Direction.RIGHT : Direction.LEFT;
        } else if (dy != 0) {
            return dy > 0 ? Direction.UP : Direction.DOWN;
        }
        
        return null; // Already at target
    }
    
    @Override
    public List<Pos> getMovingPath() {
        return new ArrayList<>(pathPoints);
    }
    
    @Override
    public void reset() {
        currentTargetIndex = 0;
        moveTimer = 0.0;
    }
    
    @Override
    public void update(double deltaTime) {
        // Strategy state is updated in getNextMove
    }
    
    public int getCurrentTargetIndex() {
        return currentTargetIndex;
    }
    
    public Pos getCurrentTarget() {
        if (pathPoints.isEmpty()) return null;
        return pathPoints.get(currentTargetIndex);
    }
}