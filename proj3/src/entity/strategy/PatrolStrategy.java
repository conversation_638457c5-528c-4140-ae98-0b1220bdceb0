package entity.strategy;

import core.Pos;
import tileengine.TETile;
import tileengine.Tileset;
import ui.SpriteManager.Direction;
import utils.AStar;
import utils.Log;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class PatrolStrategy implements GhostStrategy {
    private TETile[][] world;
    private List<Pos> patrolPoints;
    private List<Pos> fullPatrolPath;
    private int currentPathIndex;
    private double moveTimer;
    private double moveSpeed;
    private Random random;
    
    public PatrolStrategy(TETile[][] world, Pos startPos, long seed, int numPatrolPoints, 
                         double moveSpeed, int minDistance, int maxDistance) {
        this.world = world;
        this.moveSpeed = moveSpeed;
        this.random = new Random(seed);
        this.currentPathIndex = 0;
        this.moveTimer = 0.0;
        
        generatePatrolPath(startPos, numPatrolPoints, minDistance, maxDistance);
    }
    
    /**
     * Generate a patrol path with the specified number of points using A* pathfinding
     */
    private void generatePatrolPath(Pos startPos, int numPatrolPoints, int minDistance, int maxDistance) {
        patrolPoints = new ArrayList<>();
        patrolPoints.add(startPos); // Start point is always the first patrol point
        
        Pos currentPos = startPos;
        
        // Generate patrol points
        for (int i = 1; i < numPatrolPoints; i++) {
            Pos nextPoint = findRandomReachablePosition(currentPos, minDistance, maxDistance);
            if (nextPoint != null) {
                patrolPoints.add(nextPoint);
                currentPos = nextPoint;
            } else {
                Log.debug("PatrolStrategy: Could not find reachable position for patrol point " + i);
                break;
            }
        }
        
        // If we couldn't generate enough points, log it
        if (patrolPoints.size() < numPatrolPoints) {
            Log.debug("PatrolStrategy: Generated " + patrolPoints.size() + " patrol points instead of " + numPatrolPoints);
        }
        
        // Generate full patrol path connecting all points in sequence
        generateFullPatrolPath();
    }
    
    /**
     * Find a random reachable position within the specified distance range
     */
    private Pos findRandomReachablePosition(Pos fromPos, int minDistance, int maxDistance) {
        int maxAttempts = 50; // Limit attempts to avoid infinite loops
        
        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            // Generate random position within distance range
            int distance = minDistance + random.nextInt(maxDistance - minDistance + 1);
            
            // Random angle
            double angle = random.nextDouble() * 2 * Math.PI;
            int deltaX = (int) Math.round(distance * Math.cos(angle));
            int deltaY = (int) Math.round(distance * Math.sin(angle));
            
            Pos candidatePos = new Pos(fromPos.x + deltaX, fromPos.y + deltaY);
            
            // Check if position is valid and reachable
            if (isValidPosition(candidatePos)) {
                List<Pos> path = AStar.findPath(world, fromPos, candidatePos);
                if (!path.isEmpty()) {
                    return candidatePos;
                }
            }
        }
        
        return null; // Could not find a reachable position
    }
    
    /**
     * Check if a position is valid for patrol
     */
    private boolean isValidPosition(Pos pos) {
        if (pos.x < 0 || pos.x >= world.length || pos.y < 0 || pos.y >= world[0].length) {
            return false;
        }
        
        TETile tile = world[pos.x][pos.y];
        return tile == Tileset.FLOOR; // Only floor tiles are walkable
    }
    
    /**
     * Generate the full patrol path by connecting all patrol points with A* paths
     */
    private void generateFullPatrolPath() {
        fullPatrolPath = new ArrayList<>();
        
        if (patrolPoints.size() < 2) {
            Log.debug("PatrolStrategy: Not enough patrol points to generate path");
            return;
        }
        
        // Connect each patrol point to the next
        for (int i = 0; i < patrolPoints.size(); i++) {
            Pos from = patrolPoints.get(i);
            Pos to = patrolPoints.get((i + 1) % patrolPoints.size()); // Wrap around to create loop
            
            List<Pos> segment = AStar.findPath(world, from, to);
            
            if (!segment.isEmpty()) {
                // Add all positions in segment except the first (to avoid duplicates)
                if (i == 0) {
                    fullPatrolPath.add(from); // Add starting position only once
                }
                fullPatrolPath.addAll(segment);
            } else {
                Log.debug("PatrolStrategy: Could not connect patrol point " + i + " to " + ((i + 1) % patrolPoints.size()));
                // If we can't connect patrol points, fall back to direct patrol points
                if (i == 0) {
                    fullPatrolPath.add(from);
                }
                fullPatrolPath.add(to);
            }
        }
        
        Log.debug("PatrolStrategy: Generated patrol path with " + fullPatrolPath.size() + " total positions");
        Log.debug("PatrolStrategy: Patrol points: " + patrolPoints.size());
    }
    
    @Override
    public Direction getNextMove(Pos currentPos, double deltaTime) {
        if (fullPatrolPath.isEmpty()) {
            return null;
        }
        
        moveTimer += deltaTime;
        if (moveTimer < moveSpeed) {
            return null; // Not time to move yet
        }
        
        // Find next target position
        Pos targetPos = fullPatrolPath.get(currentPathIndex);
        
        // Check if we've reached the current target
        if (currentPos.x == targetPos.x && currentPos.y == targetPos.y) {
            // Move to next position in patrol path
            currentPathIndex = (currentPathIndex + 1) % fullPatrolPath.size();
            targetPos = fullPatrolPath.get(currentPathIndex);
        }
        
        // Calculate direction to target
        Direction direction = calculateDirection(currentPos, targetPos);
        
        if (direction != null) {
            moveTimer = 0.0; // Reset timer after successful move
        }
        
        return direction;
    }
    
    /**
     * Calculate direction from current position to target
     */
    private Direction calculateDirection(Pos from, Pos to) {
        int dx = Integer.compare(to.x, from.x);
        int dy = Integer.compare(to.y, from.y);
        
        // Prioritize one axis movement (move horizontally first, then vertically)
        if (dx != 0) {
            return dx > 0 ? Direction.RIGHT : Direction.LEFT;
        } else if (dy != 0) {
            return dy > 0 ? Direction.UP : Direction.DOWN;
        }
        
        return null; // Already at target
    }
    
    @Override
    public List<Pos> getMovingPath() {
        // Return the full patrol path for visualization
        return new ArrayList<>(fullPatrolPath);
    }
    
    @Override
    public void reset() {
        currentPathIndex = 0;
        moveTimer = 0.0;
    }
    
    @Override
    public void update(double deltaTime) {
        // Strategy state is updated in getNextMove
    }
    
    /**
     * Get the patrol points for debugging
     */
    public List<Pos> getPatrolPoints() {
        return new ArrayList<>(patrolPoints);
    }
    
    /**
     * Get current path index for debugging
     */
    public int getCurrentPathIndex() {
        return currentPathIndex;
    }
    
    /**
     * Check if patrol path is valid
     */
    public boolean hasValidPath() {
        return !fullPatrolPath.isEmpty();
    }
}