package entity;

import config.GameConfig;
import core.Pos;
import entity.strategy.GhostStrategy;
import tileengine.TETile;
import ui.SpriteManager;
import ui.SpriteManager.Direction;
import ui.SpriteManager.GhostType;
import java.util.List;

public abstract class Ghost extends Entity {
    private GhostType ghostType;
    private int speed;
    public Direction currentDirection; // Current movement direction
    private int currentFrame = 0; // For animation
    private double animationTimer = 0.0; // Track time for animation
    private static final double ANIMATION_SPEED = GameConfig.GHOST_ANIMATION_SPEED; // Time between frames in seconds
    
    protected GhostStrategy strategy; // Strategy for ghost movement (e.g., chase, scatter)

    public Ghost(GhostType ghostType, int speed) {
        super(EntityType.GHOST, ghostType.name().toLowerCase(), SpriteManager.getInstance().getGhostTiles(ghostType, Direction.RIGHT));
        this.ghostType = ghostType;
        this.speed = speed;
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateTheme();
    }
    
    public Ghost(GhostType ghostType, int speed, Pos initialPosition) {
        super(EntityType.GHOST, ghostType.name().toLowerCase(), SpriteManager.getInstance().getGhostTiles(ghostType, Direction.RIGHT));
        this.ghostType = ghostType;
        this.speed = speed;
        this.position = initialPosition;
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateTheme();
    }
    
    public Ghost(GhostType ghostType, int speed, Pos initialPosition, tileengine.TETile[][] world, long seed) {
        super(EntityType.GHOST, ghostType.name().toLowerCase(), SpriteManager.getInstance().getGhostTiles(ghostType, Direction.RIGHT));
        this.ghostType = ghostType;
        this.speed = speed;
        this.position = initialPosition;
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateTheme();
        // Subclasses can override to set up strategies with world and seed
    }

    public Ghost(EntityType type, String name, TETile[] tiles) {
        super(type, name, tiles);
        //TODO Auto-generated constructor stub
    }


    public GhostType getGhostType() {
        return ghostType;
    }

    public int getSpeed() {
        return speed;
    }

    public void setDirection(Direction direction) {
        this.currentDirection = direction;
        updateTheme(); // refresh tiles for direction
    }

    public Direction getCurrentDirection() {
        return currentDirection;
    }

    public TETile getCurrentTile() {
        return tiles[currentFrame];
    }

    public void nextFrame() {
        currentFrame = (currentFrame + 1) % tiles.length;
    }

    public void updateAnimation(double deltaTime) {
        animationTimer += deltaTime;
        if (animationTimer >= ANIMATION_SPEED) {
            nextFrame();
            animationTimer = 0.0;
        }
    }
    
    /**
     * Update ghost movement based on strategy
     */
    public Direction updateMovement(double deltaTime) {
        if (strategy == null) return null;
        
        strategy.update(deltaTime);
        Direction nextMove = strategy.getNextMove(this.position, deltaTime);
        
        if (nextMove != null) {
            setDirection(nextMove);
        }
        
        return nextMove;
    }
    
    public void setStrategy(GhostStrategy strategy) {
        this.strategy = strategy;
    }
    
    public GhostStrategy getStrategy() {
        return strategy;
    }
    
    public List<Pos> getMovingPath() {
        if (strategy == null) return new java.util.ArrayList<>();
        return strategy.getMovingPath();
    }

    @Override
    public void updateTheme() {
        this.tiles = SpriteManager.getInstance().getGhostTiles(ghostType, currentDirection);
        // Clamp frame index if needed
        if (currentFrame >= tiles.length) {
            currentFrame = 0;
        }
    }

}
