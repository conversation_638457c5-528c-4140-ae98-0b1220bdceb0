package entity;

import java.awt.Color;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import config.GameConfig;
import core.Pos;
import entity.strategy.GhostStrategy;
import tileengine.TETile;
import tileengine.Tileset;
import ui.SpriteManager;
import ui.SpriteManager.Direction;
import ui.SpriteManager.GhostType;
import ui.themes.Theme;
import ui.themes.ThemeManager;

public abstract class Ghost extends Entity {
    private GhostType ghostType;
    private int speed;
    public Direction currentDirection; // Current movement direction
    private int currentFrame = 0; // For animation
    private double animationTimer = 0.0; // Track time for animation
    private static final double ANIMATION_SPEED = GameConfig.GHOST_ANIMATION_SPEED; // Time between frames in seconds

    protected GhostStrategy strategy; // Strategy for ghost movement (e.g., chase, scatter)

    // Path visualization fields
    private boolean pathVisualizationEnabled;
    private Set<Pos> pathPositions;
    private TETile pathTile;

    public Ghost(GhostType ghostType, int speed) {
        super(EntityType.GHOST, ghostType.name().toLowerCase(), SpriteManager.getInstance().getGhostTiles(ghostType, Direction.RIGHT));
        this.ghostType = ghostType;
        this.speed = speed;
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateTheme();
        initializePathVisualization();
    }
    
    public Ghost(GhostType ghostType, int speed, Pos initialPosition) {
        super(EntityType.GHOST, ghostType.name().toLowerCase(), SpriteManager.getInstance().getGhostTiles(ghostType, Direction.RIGHT));
        this.ghostType = ghostType;
        this.speed = speed;
        this.position = initialPosition;
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateTheme();
        initializePathVisualization();
    }

    public Ghost(GhostType ghostType, int speed, Pos initialPosition, tileengine.TETile[][] world, long seed) {
        super(EntityType.GHOST, ghostType.name().toLowerCase(), SpriteManager.getInstance().getGhostTiles(ghostType, Direction.RIGHT));
        this.ghostType = ghostType;
        this.speed = speed;
        this.position = initialPosition;
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateTheme();
        initializePathVisualization();
        // Subclasses can override to set up strategies with world and seed
    }

    public Ghost(EntityType type, String name, TETile[] tiles) {
        super(type, name, tiles);
        //TODO Auto-generated constructor stub
    }


    public GhostType getGhostType() {
        return ghostType;
    }

    public int getSpeed() {
        return speed;
    }

    public void setDirection(Direction direction) {
        this.currentDirection = direction;
        updateTheme(); // refresh tiles for direction
    }

    public Direction getCurrentDirection() {
        return currentDirection;
    }

    public TETile getCurrentTile() {
        return tiles[currentFrame];
    }

    public void nextFrame() {
        currentFrame = (currentFrame + 1) % tiles.length;
    }

    public void updateAnimation(double deltaTime) {
        animationTimer += deltaTime;
        if (animationTimer >= ANIMATION_SPEED) {
            nextFrame();
            animationTimer = 0.0;
        }
    }
    
    /**
     * Update ghost movement based on strategy
     */
    public Direction updateMovement(double deltaTime) {
        if (strategy == null) return null;
        
        strategy.update(deltaTime);
        Direction nextMove = strategy.getNextMove(this.position, deltaTime);
        
        if (nextMove != null) {
            setDirection(nextMove);
        }
        
        return nextMove;
    }
    
    public void setStrategy(GhostStrategy strategy) {
        this.strategy = strategy;
    }
    
    public GhostStrategy getStrategy() {
        return strategy;
    }
    
    public List<Pos> getMovingPath() {
        if (strategy == null) return new java.util.ArrayList<>();
        return strategy.getMovingPath();
    }

    @Override
    public void updateTheme() {
        this.tiles = SpriteManager.getInstance().getGhostTiles(ghostType, currentDirection);
        // Clamp frame index if needed
        if (currentFrame >= tiles.length) {
            currentFrame = 0;
        }
        // Update path tile when theme changes
        updatePathTile();
    }

    /**
     * Initialize path visualization fields
     */
    private void initializePathVisualization() {
        this.pathVisualizationEnabled = false;
        this.pathPositions = new HashSet<>();
        this.pathTile = createPathTile();

        // Register for theme updates
        ThemeManager.getInstance().addThemeListener(this);
    }

    /**
     * Create a path indicator tile that matches this ghost's color
     */
    private TETile createPathTile() {
        Color ghostColor = ghostType.getColor();
        Color backgroundColor = ThemeManager.getInstance().getColor(ui.themes.ColorKey.BLACK);
        return new TETile('o', ghostColor, backgroundColor, "path indicator for " + ghostType.name().toLowerCase() + " ghost", 4);
    }

    /**
     * Update the path tile when theme changes
     */
    private void updatePathTile() {
        this.pathTile = createPathTile();
    }

    // ===== PATH VISUALIZATION METHODS =====

    /**
     * Enable or disable path visualization for this ghost
     */
    public void setPathVisualizationEnabled(boolean enabled) {
        this.pathVisualizationEnabled = enabled;
        if (enabled) {
            updatePathVisualization();
        } else {
            pathPositions.clear();
        }
    }

    /**
     * Toggle path visualization on/off for this ghost
     */
    public void togglePathVisualization() {
        setPathVisualizationEnabled(!pathVisualizationEnabled);
    }

    /**
     * Check if path visualization is enabled for this ghost
     */
    public boolean isPathVisualizationEnabled() {
        return pathVisualizationEnabled;
    }

    /**
     * Update path visualization based on current strategy
     */
    public void updatePathVisualization() {
        pathPositions.clear();

        if (!pathVisualizationEnabled) {
            return;
        }

        List<Pos> ghostPath = getMovingPath();
        if (ghostPath != null) {
            pathPositions.addAll(ghostPath);
        }
    }

    /**
     * Get all positions where this ghost's path indicators should be rendered
     */
    public Set<Pos> getPathPositions() {
        return new HashSet<>(pathPositions);
    }

    /**
     * Check if a position should show a path indicator for this ghost
     */
    public boolean hasPathIndicator(int x, int y) {
        return pathVisualizationEnabled && pathPositions.contains(new Pos(x, y));
    }

    /**
     * Check if a position should show a path indicator for this ghost
     */
    public boolean hasPathIndicator(Pos pos) {
        return pathVisualizationEnabled && pathPositions.contains(pos);
    }

    /**
     * Get the tile to use for this ghost's path indicators
     */
    public TETile getPathTile() {
        return pathTile;
    }

    /**
     * Render this ghost's path indicators to the world array
     * This modifies the world array directly for path visualization
     */
    public void renderPathToWorld(TETile[][] world) {
        if (!pathVisualizationEnabled || world == null) {
            return;
        }

        for (Pos pos : pathPositions) {
            if (pos.x >= 0 && pos.x < world.length &&
                pos.y >= 0 && pos.y < world[0].length) {

                // Only render path indicators on floor tiles (don't overwrite walls/entities)
                if (world[pos.x][pos.y] == Tileset.FLOOR) {
                    world[pos.x][pos.y] = pathTile;
                }
            }
        }
    }

    /**
     * Render this ghost's path indicators at their positions.
     * This is the Renderable interface implementation for path visualization.
     */
    public void renderPath() {
        if (!pathVisualizationEnabled) {
            return;
        }

        // Render each path indicator at its position
        for (Pos pos : pathPositions) {
            pathTile.draw(pos.x, pos.y);
        }
    }

    /**
     * Get statistics about this ghost's path visualization
     */
    public String getPathStats() {
        if (!pathVisualizationEnabled) {
            return ghostType.name() + " ghost path visualization: OFF";
        }

        return String.format("%s ghost path visualization: ON (%d positions)",
                           ghostType.name(), pathPositions.size());
    }

    /**
     * Clear all path data for this ghost
     */
    public void clearPath() {
        pathPositions.clear();
    }

    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        // Call parent implementation first
        super.onThemeChanged(oldTheme, newTheme);
        // Update path tile to use new theme colors
        updatePathTile();
    }

}
