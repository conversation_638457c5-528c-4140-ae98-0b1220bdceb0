package entity.ghosts;

import core.Pos;
import entity.Ghost;
import entity.strategy.PatrolStrategy;
import tileengine.TETile;
import ui.SpriteManager.GhostType;
import utils.Log;

public class BlueGhost extends Ghost {
    public BlueGhost(Pos initialPosition, TETile[][] world, long seed) {
        super(GhostType.BLUE, 1, initialPosition, world, seed);
        initializeRandomStrategy(world, seed);
    }

    private void initializeRandomStrategy(TETile[][] world, long seed) {
        // Generate an A*-based patrol path with random reachable points
        // Use position to make each ghost instance unique but deterministic
        long ghostSeed = seed + (this.position.x * 1000L) + (this.position.y * 31L);

        PatrolStrategy patrolStrategy = new PatrolStrategy(
                world,
                this.position,
                ghostSeed,
                5, // Number of patrol points
                0.5, // Move speed (time between moves)
                3, // Minimum distance between patrol points
                6 // Maximum distance between patrol points
        );

        if (patrolStrategy.hasValidPath()) {
            this.strategy = patrolStrategy;
            Log.debug("BlueGhost generated A*-based patrol path with " +
                    patrolStrategy.getPatrolPoints().size() + " patrol points");
        } else {
            Log.debug("BlueGhost falling back to default strategy");
        }
    }

}
