package entity.ghosts;

import core.Pos;
import entity.Ghost;
import entity.strategy.PolygonPathStrategy;
import ui.SpriteManager.GhostType;

public class PinkGhost extends Ghost {

    public PinkGhost() {
        super(GhostType.PINK, 1);
        initializeStrategy();
    }
    
    public PinkGhost(Pos initialPosition) {
        super(GhostType.PINK, 1, initialPosition);
        initializeStrategy();
    }
    
    private void initializeStrategy() {
        // PinkGhost follows a pentagon path
        Pos[] path = {
            new Pos(20, 6), 
            new Pos(25, 6), 
            new Pos(27, 10),
            new Pos(23, 13),
            new Pos(17, 10)
        };
        this.strategy = new PolygonPathStrategy(path, 0.6); // Move every 0.6 seconds
    }

}
