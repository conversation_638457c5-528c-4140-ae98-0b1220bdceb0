package entity.ghosts;

import core.Pos;
import entity.Ghost;
import entity.strategy.PolygonPathStrategy;
import ui.SpriteManager.GhostType;

public class OrangeGhost extends Ghost {

    public OrangeGhost() {
        super(GhostType.ORANGE, 1);
        initializeStrategy();
    }
    
    public OrangeGhost(Pos initialPosition) {
        super(GhostType.ORANGE, 1, initialPosition);
        initializeStrategy();
    }
    
    private void initializeStrategy() {
        // OrangeGhost follows a complex hexagon path (example from your request)
        Pos[] path = {
            new Pos(3, 4), 
            new Pos(3, 8), 
            new Pos(7, 8), 
            new Pos(7, 5),
            new Pos(5, 5),
            new Pos(5, 4)
        };
        this.strategy = new PolygonPathStrategy(path, 0.3); // Move every 0.3 seconds
    }

}
