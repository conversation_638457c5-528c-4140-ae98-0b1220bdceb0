package entity.ghosts;

import config.GameConfig;
import core.Pos;
import entity.Ghost;
import entity.Player;
import entity.strategy.TrackerStrategy;
import entity.strategy.PolygonPathStrategy;
import tileengine.TETile;
import ui.SpriteManager.GhostType;

public class RedGhost extends Ghost {

    public RedGhost() {
        super(GhostType.RED, 1);
        initializeDefaultStrategy();
    }
    
    public RedGhost(Pos initialPosition) {
        super(GhostType.RED, 1, initialPosition);
        initializeDefaultStrategy();
    }
    
    public RedGhost(Pos initialPosition, TETile[][] world, Player targetPlayer) {
        super(GhostType.RED, 1, initialPosition, world, 0);
        initializeAStarStrategy(world, targetPlayer);
    }
    
    private void initializeDefaultStrategy() {
        // RedGhost follows a triangle path when no player target is available
        Pos[] path = {
            new Pos(10, 5), 
            new Pos(15, 5), 
            new Pos(12, 10)
        };
        this.strategy = new PolygonPathStrategy(path, 0.4); // Move every 0.4 seconds
    }
    
    private void initializeAStarStrategy(TETile[][] world, Player targetPlayer) {
        // RedGhost uses A* pathfinding to chase the player
        this.strategy = new TrackerStrategy(
            world, 
            targetPlayer,
            GameConfig.RED_GHOST_MOVE_SPEED,
            GameConfig.RED_GHOST_PATH_REFRESH_INTERVAL
        );
    }

}
