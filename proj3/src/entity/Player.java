package entity;

import config.GameConfig;
import core.Pos;
import tileengine.TETile;
import ui.SpriteManager;
import ui.SpriteManager.Direction;

public class Player extends Entity {
    private Direction currentDirection;
    private int currentFrame = 0; // For animation
    private double animationTimer = 0.0; // Track time for animation
    private static final double ANIMATION_SPEED = GameConfig.PLAYER_ANIMATION_SPEED; // Time between frames in seconds

    public Player() {
        super(EntityType.PLAYER, "pacman", createDefaultPlayerTile());
        this.currentDirection = Direction.RIGHT; // Default facing direction
        // Initialize with all 4 animation frames
        updateSprites();
    }
    
    public Player(Pos initialPosition) {
        super(EntityType.PLAYER, "pacman", createDefaultPlayerTile());
        this.currentDirection = Direction.RIGHT; // Default facing direction
        this.position = initialPosition;
        // Initialize with all 4 animation frames
        updateSprites();
    }
    
    private static TETile createDefaultPlayerTile() {
        return SpriteManager.getInstance().getAvatarTiles(Direction.RIGHT)[0];
    }
    
    /**
     * Update player direction and sprite.
     */
    public void setDirection(Direction direction) {
        this.currentDirection = direction;
        updateSprites();
    }
    
    private void updateSprites() {
        this.tiles = SpriteManager.getInstance().getAvatarTiles(currentDirection);
    }
    
    public Direction getCurrentDirection() {
        return currentDirection;
    }
    
    /**
     * Get current animation frame tile for rendering
     */
    public TETile getCurrentTile() {
        return tiles[currentFrame];
    }
    
    /**
     * Advance animation frame (for movement animation)
     */
    public void nextFrame() {
        currentFrame = (currentFrame + 1) % tiles.length;
    }
    
    /**
     * Update animation based on delta time
     */
    public void updateAnimation(double deltaTime) {
        animationTimer += deltaTime;
        if (animationTimer >= ANIMATION_SPEED) {
            nextFrame();
            animationTimer = 0.0;
        }
    }
    
    public int getCurrentFrame() {
        return currentFrame;
    }
    
    @Override
    public void updateTheme() {
        // Refresh sprites with current direction and new theme
        updateSprites();
    }
    
    /**
     * Handle movement input and return the desired movement direction.
     * @param key the movement key pressed
     * @return Direction object representing desired movement, or null if invalid key
     */
    public Direction handleMovementInput(char key) {
        key = Character.toLowerCase(key);
        
        switch (key) {
            case 'w':
            case 'W':
                setDirection(Direction.UP);
                return Direction.UP;
            case 'a':
            case 'A':
                setDirection(Direction.LEFT);
                return Direction.LEFT;
            case 's':
            case 'S':
                setDirection(Direction.DOWN);
                return Direction.DOWN;
            case 'd':
            case 'D':
                setDirection(Direction.RIGHT);
                return Direction.RIGHT;
            default:
                return null;
        }
    }
}
