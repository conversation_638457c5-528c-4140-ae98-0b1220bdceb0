package core;

import ui.themes.Theme;
import ui.themes.ThemeManager;

public abstract class Screen implements Renderable {
    public int width;
    public int height;

    public ThemeManager themeManager = ThemeManager.getInstance();

    public Screen(int width, int height) {
        this.width = width;
        this.height = height;
        
        // Register this screen as a theme listener
        themeManager.addThemeListener(this);
    }

    public abstract void render();

    public abstract void handleInput();

    public abstract void update(double deltaTime);
    
    /**
     * Called when the theme changes. Subclasses should override this
     * to update their cached colors and force a redraw.
     */
    @Override
    public abstract void onThemeChanged(Theme oldTheme, Theme newTheme);
}
