package core;

import utils.Log;

/**
 * Centralized state management for the Pac-Man game.
 * Defines all possible game states and manages transitions between them.
 * <p>
 * Game State Flow:
 * START_MENU -> HUB_WORLD -> LEVEL_WORLD -> PAUSED -> LEVEL_WORLD
 * -> SETTINGS -> START_MENU
 * -> LEVEL_WORLD -> GAME_OVER -> START_MENU
 */
public enum GameState {
    /**
     * Initial state when game launches.
     * Shows main menu with options: New Game, Load Game, Settings, Quit.
     */
    START_MENU("Start Menu"),

    /**
     * Game is temporarily halted.
     * Triggered by ESC key during LEVEL_WORLD state.
     * Shows pause menu with options: Resume, Settings, Main Menu.
     */
    PAUSED("Paused"),

    /**
     * Terminal state when player dies or quits.
     * Shows final score, high score comparison, and options: Restart, Main Menu.
     */
    GAME_OVER("Game Over"),

    /**
     * Configuration state accessible from START_MENU or PAUSED.
     * Allows adjustment of: Audio volume, Theme selection, Controls.
     */
    SETTINGS("Settings"),

    /**
     * Hub world navigation state.
     * Player can move around hub world and select levels to enter.
     * Shows visual indicators of level unlock status and completion.
     */
    HUB_WORLD("Hub World"),

    /**
     * Active level gameplay state.
     * Player is playing within a specific level with defined objectives.
     * Similar to PLAYING but with level-specific goals and constraints.
     */
    LEVEL_WORLD("Level World"),

    /**
     * Seed input state.
     * Player is entering a seed value to generate the world.
     * Shows live input feedback and allows pressing S to start game.
     */
    SEED_INPUT("Seed Input");

    // Human-readable name for the state
    private final String displayName;

    /**
     * Constructor for GameState enum.
     *
     * @param displayName Human-readable name for the state
     */
    GameState(String displayName) {
        this.displayName = displayName;
    }

    /**
     * Determines if a transition from this state to the target state is valid.
     * Prevents invalid state transitions that could break the game flow.
     * <p>
     * Valid transitions:
     * - START_MENU -> HUB_WORLD, SETTINGS, SEED_INPUT
     * - HUB_WORLD -> LEVEL_WORLD, PAUSED, START_MENU
     * - LEVEL_WORLD -> HUB_WORLD, PAUSED, GAME_OVER
     * - PAUSED -> (previous state: HUB_WORLD, or LEVEL_WORLD)
     * - GAME_OVER -> START_MENU, HUB_WORLD
     * - SETTINGS -> START_MENU, PAUSED (depending on origin)
     *
     * @param target The target state to transition to
     * @return true if the transition is allowed, false otherwise
     */
    public boolean canTransition(GameState target) {
        if (this == START_MENU) {
            return target == HUB_WORLD || target == SETTINGS || target == SEED_INPUT;
        } else if (this == HUB_WORLD) {
            return target == LEVEL_WORLD || target == PAUSED || target == START_MENU;
        } else if (this == LEVEL_WORLD) {
            return target == HUB_WORLD || target == PAUSED || target == GAME_OVER;
        } else if (this == PAUSED) {
            return target == HUB_WORLD || target == LEVEL_WORLD || target == SETTINGS || target == START_MENU;
        } else if (this == GAME_OVER) {
            return target == START_MENU || target == HUB_WORLD;
        } else if (this == SETTINGS) {
            return target == START_MENU || target == PAUSED;
        } else if (this == SEED_INPUT) {
            return target == HUB_WORLD || target == START_MENU;
        } else {
            Log.warn(displayName + " cannot transition to " + target.displayName);
            return false;
        }
    }

    /**
     * Gets a human-readable string representation of the state.
     * Used for debugging, logging, and potentially UI display.
     * <p>
     * - START_MENU -> "Main Menu"
     * - PLAYING -> "Playing"
     * - PAUSED -> "Paused"
     * - GAME_OVER -> "Game Over"
     * - SETTINGS -> "Settings"
     *
     * @return A descriptive string for the current state
     */
    public String getDisplayName() {
        return displayName;
    }
}