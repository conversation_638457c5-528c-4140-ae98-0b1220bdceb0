package core;

import ui.themes.Theme;
import ui.themes.ThemeManager;

/**
 * Unified interface for all renderable objects in the game.
 * Combines rendering capability with automatic theme updates.
 * 
 * This interface provides a consistent contract for:
 * - Rendering visual elements to the screen
 * - Automatic theme change notifications
 * - Resource cleanup when objects are disposed
 */
public interface Renderable extends ThemeManager.ThemeListener {
    
    /**
     * Render this object to the screen.
     * Implementations should handle their own rendering logic
     * and respect the current theme settings.
     */
    void render();
    
    /**
     * Called when the active theme changes.
     * Implementations should update their visual elements
     * to match the new theme.
     * 
     * @param oldTheme The previously active theme (may be null)
     * @param newTheme The new active theme
     */
    @Override
    void onThemeChanged(Theme oldTheme, Theme newTheme);
    
    /**
     * Clean up resources and unregister theme listeners.
     * Should be called when this renderable object is no longer needed.
     * 
     * Default implementation removes this object from theme notifications.
     */
    default void dispose() {
        ThemeManager.getInstance().removeThemeListener(this);
    }
}