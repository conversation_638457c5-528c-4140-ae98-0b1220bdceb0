package core;

import java.util.HashMap;
import java.util.Map;
import java.util.Stack;

import config.GameConfig;
import edu.princeton.cs.algs4.StdDraw;
import menu.Menu;
import menu.PauseMenu;
import menu.SeedInputMenu;
import menu.SettingsMenu;
import menu.StartMenu;
import utils.Log;
import world.HubWorld;

public class GameEngine {
    private GameState currentState;
    private boolean isRunning;
    private long lastUpdateTime;
    private double deltaTime;

    public Screen currentScreen;
    private Stack<ScreenStackEntry> screenStack;
    
    private static class ScreenStackEntry {
        public final GameState state;
        public final Screen screen;
        
        public ScreenStackEntry(GameState state, Screen screen) {
            this.state = state;
            this.screen = screen;
        }
    }

    private int worldWidth = GameConfig.WIDTH;
    private int worldHeight = GameConfig.HEIGHT;

    private long seed = GameConfig.DEFAULT_SEED; // Default seed value

    private Map<GameState, Menu> menus = new HashMap<>();
    private PauseMenu pauseMenu;
    private HubWorld hubWorld;

    public GameEngine() {
        screenStack = new Stack<>();
        
        menus.put(GameState.START_MENU, new StartMenu(this));
        menus.put(GameState.SEED_INPUT, new SeedInputMenu(this));
        pauseMenu = new PauseMenu(this);
        menus.put(GameState.PAUSED, pauseMenu);

        hubWorld = new HubWorld(worldWidth, worldHeight, seed, this);
        hubWorld.generateWorld();

        currentState = GameState.START_MENU;
        currentScreen = menus.get(currentState);
        
        // Initialize the screen stack with the starting screen
        screenStack.push(new ScreenStackEntry(currentState, currentScreen));

        isRunning = false;
        lastUpdateTime = System.currentTimeMillis();        
    }

    public void gameLoop() {
        // Main game loop logic would go here
        // This is where the game state would be updated based on user input, game
        // events, etc.
        isRunning = true;
        Log.info("Game loop started. Current state: " + currentState.getDisplayName());
        while (isRunning) {
            long currentTime = System.currentTimeMillis();
            deltaTime = (currentTime - lastUpdateTime) / 1000.0;
            lastUpdateTime = currentTime;
            
            currentScreen.handleInput();
            currentScreen.update(deltaTime);
            currentScreen.render();

            StdDraw.pause(GameConfig.UPDATE_RATE_MS); // Control frame rate
        }
    }

    public void setState(GameState newState) {
        if (!currentState.canTransition(newState)) {
            Log.warn(currentState.getDisplayName() + " cannot transition to " + newState.getDisplayName());
            return;
        }

        GameState previousState = currentState;
        currentState = newState;
        
        // Determine screen stack operation based on transition type
        if (isModalTransition(previousState, newState)) {
            // Push new screen onto stack (modal/overlay states like PAUSED, SETTINGS)
            pushScreen(newState);
        } else if (isReturnTransition(previousState, newState)) {
            // Pop back to previous screen
            popScreen();
        } else {
            // Replace current screen (main state changes like HUB_WORLD -> LEVEL_WORLD)
            replaceScreen(newState);
        }
        
        Log.info("Switched to screen for state: " + newState.getDisplayName());
    }
    
    private boolean isModalTransition(GameState from, GameState to) {
        // These states are modal overlays that should be pushed onto the stack
        return (from == GameState.HUB_WORLD && to == GameState.PAUSED) ||
               (from == GameState.LEVEL_WORLD && to == GameState.PAUSED) ||
               (from == GameState.PAUSED && to == GameState.SETTINGS) ||
               (from == GameState.START_MENU && to == GameState.SETTINGS);
    }
    
    private boolean isReturnTransition(GameState from, GameState to) {
        // These are explicit returns to previous screens
        return (from == GameState.SETTINGS && to == GameState.PAUSED) ||
               (from == GameState.SETTINGS && to == GameState.START_MENU) ||
               (from == GameState.PAUSED && (to == GameState.HUB_WORLD || to == GameState.LEVEL_WORLD));
    }
    
    private void pushScreen(GameState newState) {
        Screen newScreen = createScreenForState(newState);
        screenStack.push(new ScreenStackEntry(newState, newScreen));
        currentScreen = newScreen;
        Log.debug("Pushed screen for state: " + newState.getDisplayName() + " (stack size: " + screenStack.size() + ")");
    }
    
    private void popScreen() {
        if (screenStack.size() > 1) {
            ScreenStackEntry poppedEntry = screenStack.pop();
            ScreenStackEntry previousEntry = screenStack.peek();
            
            currentState = previousEntry.state;
            currentScreen = previousEntry.screen;
            
            Log.debug("Popped screen " + poppedEntry.state.getDisplayName() + 
                     ", returned to " + previousEntry.state.getDisplayName() + 
                     " (stack size: " + screenStack.size() + ")");
        } else {
            Log.warn("Cannot pop screen: stack would be empty");
        }
    }
    
    private void replaceScreen(GameState newState) {
        // Replace the top of the stack with new screen
        if (!screenStack.isEmpty()) {
            screenStack.pop();
        }
        
        Screen newScreen = createScreenForState(newState);
        screenStack.push(new ScreenStackEntry(newState, newScreen));
        currentScreen = newScreen;
        Log.debug("Replaced screen with state: " + newState.getDisplayName() + " (stack size: " + screenStack.size() + ")");
    }
    
    private Screen createScreenForState(GameState state) {
        switch (state) {
            case PAUSED:
                return pauseMenu;
            case HUB_WORLD:
                return hubWorld;
            case SETTINGS:
                return new SettingsMenu(this);
            default:
                // Check if it's a menu state
                Menu menu = menus.get(state);
                if (menu != null) {
                    return menu;
                } else {
                    Log.warn("No screen found for state: " + state.getDisplayName());
                    return currentScreen; // Fallback to current screen
                }
        }
    }

    public GameState getState() {
        return currentState;
    }


    public int getWorldWidth() {
        return worldWidth;
    }   

    public int getWorldHeight() {
        return worldHeight;
    }

    public long getCurrentSeed() {
        return seed;
    }

    public void setSeed(long seed) {
        this.seed = seed;
        Log.info("Seed set to: " + seed);
    }
}
