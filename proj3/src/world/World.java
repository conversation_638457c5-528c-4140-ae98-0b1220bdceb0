package world;

import core.Screen;
import ui.themes.Theme;

public abstract class World extends Screen {
    // Color wallColor = themeManager.getColor(ColorKey.WALL);
    
    public World(int width, int height) {
        super(width, height);
    }

    @Override
    public void render() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'render'");
    }

    @Override
    public void handleInput() {
        // TODO Auto-generated method stub
        throw new UnsupportedOperationException("Unimplemented method 'handleInput'");
    }
    
    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        // Default implementation for worlds - override if world uses theme colors
        // Most worlds might not need theme updates, but this provides the hook
    }
    
}
