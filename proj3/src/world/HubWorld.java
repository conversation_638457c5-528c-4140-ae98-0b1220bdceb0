package world;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import config.GameConfig;
import core.GameEngine;
import core.GameState;
import core.Pos;
import edu.princeton.cs.algs4.StdDraw;
import entity.Ghost;
import entity.Player;
import entity.ghosts.BlueGhost;
import entity.ghosts.OrangeGhost;
import entity.ghosts.PinkGhost;
import entity.ghosts.RedGhost;
import tileengine.TERenderer;
import tileengine.TETile;
import tileengine.Tileset;
import ui.SpriteManager.Direction;
import ui.SpriteManager.GhostType;
import ui.PathVisualization;
import ui.themes.Theme;
import utils.Log;

public class HubWorld extends World {
    TETile[][] world;
    public TERenderer ter;
    Random rand;
    private long worldSeed;

    private boolean colonMode = false;
    private boolean showGhostPaths = false; // Flag for 't' key functionality
    private boolean showHoverText = false; // Flag for 'h' key functionality

    // Game entities
    private Player player;
    private GameEngine gameEngine;
    private List<Ghost> ghosts; // List to hold all ghost entities

    private GhostType[] allowedGhostTypes; // configurable ghost types
    
    // Path visualization system
    private PathVisualization pathVisualization;

    public HubWorld(long seed) {
        this(GameConfig.WIDTH, GameConfig.HEIGHT, seed);
    }

    public HubWorld(int width, int height, long seed) {
        this(width, height, seed, null);
    }

    public HubWorld(int width, int height, long seed, GameEngine gameEngine) {
        super(width, height);
        this.worldSeed = seed;
        this.rand = new Random(seed);
        this.gameEngine = gameEngine;
        this.ghosts = new ArrayList<>();
        world = new TETile[width][height];
        ter = new TERenderer();
        ter.initialize(width, height);
        this.allowedGhostTypes = new GhostType[] { GhostType.RED, GhostType.BLUE }; // default
    }

    // Overloaded constructor to configure allowed ghost types
    public HubWorld(int width, int height, long seed, GameEngine gameEngine, GhostType... allowedGhostTypes) {
        this(width, height, seed, gameEngine);
        if (allowedGhostTypes != null && allowedGhostTypes.length > 0) {
            this.allowedGhostTypes = allowedGhostTypes.clone();
        }
    }

    // Setter to reconfigure allowed ghost types at runtime
    public void setAllowedGhostTypes(GhostType... types) {
        if (types == null || types.length == 0) {
            Log.warn("Ignored attempt to set empty ghost type list");
            return;
        }
        this.allowedGhostTypes = types.clone();
        Log.info("Allowed ghost types updated (" + types.length + ")");
    }

    public GhostType[] getAllowedGhostTypes() {
        return allowedGhostTypes.clone();
    }

    public void generateWorld() {
        for (int x = 0; x < world.length; x++) {
            for (int y = 0; y < world[0].length; y++) {
                world[x][y] = Tileset.NOTHING;
            }
        }
        generateWorld(world);
        spawnPlayer();
        spawnGhosts();
        
        // Initialize path visualization system
        pathVisualization = new PathVisualization(ghosts);
        pathVisualization.setEnabled(showGhostPaths);
    }

    // New helper: find a random FLOOR tile (returns null if none)
    private Pos findRandomFloorSpawnPos() {
        List<Pos> candidates = new ArrayList<>();
        for (int x = 0; x < world.length; x++) {
            for (int y = 0; y < world[0].length; y++) {
                if (world[x][y] == Tileset.FLOOR) {
                    candidates.add(new Pos(x, y));
                }
            }
        }
        if (candidates.isEmpty()) {
            return null;
        }
        return candidates.get(rand.nextInt(candidates.size()));
    }

    private void spawnPlayer() {
        Pos spawn = findRandomFloorSpawnPos();
        if (spawn == null) {
            Log.debug("Warning: No floor tile found for player spawn");
            return;
        }
        player = new Player(spawn);
        Log.debug("Player spawned at: (" + spawn.x + ", " + spawn.y + ")");
    }

    private void spawnGhosts() {
        if (player == null) {
            Log.debug("Warning: Cannot spawn ghosts before player");
            return;
        }
        if (allowedGhostTypes == null || allowedGhostTypes.length == 0) {
            Log.debug("No allowed ghost types; skipping all ghost spawns");
            return;
        }

        List<Pos> occupiedPositions = new ArrayList<>();
        occupiedPositions.add(player.getPosition());
        
        int redGhostCount = 0; // Track number of red ghosts spawned

        for (int i = 0; i < GameConfig.GHOST_COUNT; i++) {
            Pos spawn;
            int safety = 1000;
            do {
                spawn = findRandomFloorSpawnPos();
                safety--;
            } while (spawn != null && isPositionOccupied(spawn, occupiedPositions) && safety > 0);

            if (spawn == null) {
                Log.debug("Warning: No floor tile found for ghost " + (i + 1) + " spawn");
                continue;
            }

            Ghost ghost = createRandomGhost(spawn, redGhostCount);
            if (ghost == null) {
                Log.debug("Ghost creation returned null; skipping");
                continue;
            }
            
            // Update red ghost count if we spawned a red ghost
            if (ghost instanceof RedGhost) {
                redGhostCount++;
            }
            
            ghosts.add(ghost);
            occupiedPositions.add(spawn);
            Log.debug("Ghost " + (i + 1) + " (" + ghost.getClass().getSimpleName() + ") spawned at: (" + spawn.x + ", "
                    + spawn.y + ")");
        }
    }

    private boolean isPositionOccupied(Pos pos, List<Pos> occupiedPositions) {
        for (Pos occupied : occupiedPositions) {
            if (pos.x == occupied.x && pos.y == occupied.y) {
                return true;
            }
        }
        return false;
    }

    private Ghost createRandomGhost(Pos position, int redGhostCount) {
        if (allowedGhostTypes == null || allowedGhostTypes.length == 0) {
            Log.debug("No allowed ghost types configured; skipping ghost spawn");
            return null;
        }
        
        // Filter out RED ghost type if we already have 1 red ghost
        List<GhostType> availableTypes = new ArrayList<>();
        for (GhostType type : allowedGhostTypes) {
            if (type == GhostType.RED && redGhostCount >= 1) {
                continue; // Skip red ghost if we already have 1
            }
            availableTypes.add(type);
        }
        
        // If no available types (e.g., only RED was allowed but we have 1 already), return null
        if (availableTypes.isEmpty()) {
            Log.debug("No available ghost types (red ghost limit reached)");
            return null;
        }
        
        GhostType selectedType = availableTypes.get(rand.nextInt(availableTypes.size()));
        switch (selectedType) {
            case BLUE:
                return new BlueGhost(position, world, worldSeed);
            case RED:
                return new RedGhost(position, world, player);
            case PINK:
                return new PinkGhost(position);
            case ORANGE:
                return new OrangeGhost(position);
            default:
                return new BlueGhost(position, world, worldSeed); // fallback
        }
    }

    public Player getPlayer() {
        return player;
    }

    public Pos getPlayerPos() {
        return player != null ? player.getPosition() : null;
    }

    public void generateWorld(TETile[][] world) {
        generateWorld(world, rand);
    }

    private void generateWorld(TETile[][] world, Random rand) {
        List<Room> rooms = new ArrayList<>();

        generateRandomRooms(world, rooms, rand, 15, 80); // 30 rooms, 50 attempts each

        int totalArea = width * height;
        int roomArea = calculateRoomArea(rooms);
        double coverage = (double) roomArea / totalArea * 100;

        Log.debug("Generated " + rooms.size() + " rooms");
        Log.debug("Room coverage: " + String.format("%.1f", coverage) + "%");
        Log.debug("Total world area: " + totalArea + ", Room area: " + roomArea);

        connectAllRoomsWithMST(world, rooms);
    }

    private void generateRandomRooms(TETile[][] world, List<Room> rooms, Random rand, int numRooms, int maxAttempts) {
        for (int i = 0; i < numRooms; i++) {
            for (int attempt = 0; attempt < maxAttempts; attempt++) {
                int roomWidth = 6 + rand.nextInt(13); // Rooms between 6 and 18 units wide
                int roomHeight = 6 + rand.nextInt(10); // Rooms between 6 and 15 units high

                int x = rand.nextInt(width - roomWidth - 1) + 1;
                int y = rand.nextInt(height - roomHeight - 1) + 1;

                Room newRoom = new Room(x, y, roomWidth, roomHeight);

                boolean overlaps = false;
                for (Room existing : rooms) {
                    if (newRoom.intersects(existing)) {
                        overlaps = true;
                        break;
                    }
                }

                if (!overlaps) {
                    addRoom(world, newRoom);
                    rooms.add(newRoom);
                    break; // Successfully placed room
                }
            }
        }
    }

    private int calculateRoomArea(List<Room> rooms) {
        int totalArea = 0;
        for (Room room : rooms) {
            totalArea += room.width * room.height;
        }
        return totalArea;
    }

    private void addRoom(TETile[][] world, Room r) {
        for (int x = r.x; x < r.x + r.width; x++) {
            for (int y = r.y; y < r.y + r.height; y++) {
                if (x == r.x || x == r.x + r.width - 1 || y == r.y || y == r.y + r.height - 1) {
                    world[x][y] = Tileset.WALL;
                } else {
                    world[x][y] = Tileset.FLOOR;
                }
            }
        }
    }

    private void connectAllRoomsWithMST(TETile[][] world, List<Room> rooms) {
        List<Edge> edges = new ArrayList<>();

        for (int i = 0; i < rooms.size(); i++) {
            for (int j = i + 1; j < rooms.size(); j++) {
                double dist = distance(rooms.get(i), rooms.get(j));
                edges.add(new Edge(i, j, dist));
            }
        }

        UnionFind uf = new UnionFind(rooms.size());

        for (Edge e : edges) {
            if (uf.union(e.roomA, e.roomB)) {
                connectRooms(world, rooms.get(e.roomA), rooms.get(e.roomB));
            }
        }
    }

    private double distance(Room r1, Room r2) {
        int x1 = r1.centerX(), y1 = r1.centerY();
        int x2 = r2.centerX(), y2 = r2.centerY();
        return Math.hypot(x1 - x2, y1 - y2);
    }

    private void connectRooms(TETile[][] world, Room r1, Room r2) {
        int x1 = r1.centerX(), y1 = r1.centerY();
        int x2 = r2.centerX(), y2 = r2.centerY();

        // Drunken walk for hallway generation
        int currentX = x1;
        int currentY = y1;

        while (currentX != x2 || currentY != y2) {
            boolean moveX = (currentX != x2) && (rand.nextBoolean() || currentY == y2);

            if (moveX) {
                int nextX = currentX < x2 ? currentX + 1 : currentX - 1;
                drawHorizontalCorridor(world, currentX, nextX, currentY);
                currentX = nextX;
            } else {
                int nextY = currentY < y2 ? currentY + 1 : currentY - 1;
                drawVerticalCorridor(world, currentY, nextY, currentX);
                currentY = nextY;
            }
        }
    }

    private void drawHorizontalCorridor(TETile[][] world, int xStart, int xEnd, int y) {
        for (int x = Math.min(xStart, xEnd); x <= Math.max(xStart, xEnd); x++) {
            if (changeToFloorOrWall(world[x][y]))
                world[x][y] = Tileset.FLOOR;
            if (changeToFloorOrWall(world[x][y + 1]))
                world[x][y + 1] = Tileset.WALL;
            if (changeToFloorOrWall(world[x][y - 1]))
                world[x][y - 1] = Tileset.WALL;
        }
    }

    private void drawVerticalCorridor(TETile[][] world, int yStart, int yEnd, int x) {
        for (int y = Math.min(yStart, yEnd); y <= Math.max(yStart, yEnd); y++) {
            if (changeToFloorOrWall(world[x][y]))
                world[x][y] = Tileset.FLOOR;
            if (changeToFloorOrWall(world[x + 1][y]))
                world[x + 1][y] = Tileset.WALL;
            if (changeToFloorOrWall(world[x - 1][y]))
                world[x - 1][y] = Tileset.WALL;
        }
    }

    private boolean changeToFloorOrWall(TETile tile) {
        return tile == Tileset.NOTHING || tile == Tileset.WALL;
    }

    @Override
    public void render() {
        // Reset font settings in case they were changed by menus
        ter.resetFont();

        // Create a copy of the world for entity overlay rendering
        TETile[][] renderWorld = copyWorldArray(world);
        
        // Add path visualization if enabled
        if (showGhostPaths && pathVisualization != null) {
            pathVisualization.updatePathVisualization();
            pathVisualization.renderToWorld(renderWorld);
        }

        // Overlay player if it exists
        if (player != null && player.getPosition() != null) {
            Pos playerPos = player.getPosition();
            renderWorld[playerPos.x][playerPos.y] = player.getCurrentTile();
        }

        // Overlay all ghosts
        for (Ghost ghost : ghosts) {
            if (ghost != null && ghost.getPosition() != null) {
                Pos ghostPos = ghost.getPosition();
                renderWorld[ghostPos.x][ghostPos.y] = ghost.getCurrentTile();
            }
        }

        // Render the world first, but don't show yet if we need to add hover text
        if (showHoverText) {
            StdDraw.clear(Color.BLACK);
            ter.drawTiles(renderWorld);
            renderHoverText(renderWorld);
            StdDraw.show();
        } else {
            ter.renderFrame(renderWorld);
        }
    }

    /**
     * Render hover text showing the description of the tile under the mouse cursor
     */
    private void renderHoverText(TETile[][] renderWorld) {
        // Get mouse position in screen coordinates
        double mouseX = StdDraw.mouseX();
        double mouseY = StdDraw.mouseY();

        // Convert to tile coordinates
        int tileX = (int) Math.floor(mouseX);
        int tileY = (int) Math.floor(mouseY);

        // Check if mouse is within world bounds
        if (tileX >= 0 && tileX < renderWorld.length &&
                tileY >= 0 && tileY < renderWorld[0].length) {

            TETile hoveredTile = renderWorld[tileX][tileY];
            if (hoveredTile != null) {
                String description = hoveredTile.description();

                // Render the description text in the top-left corner
                ter.resetFont();
                StdDraw.setPenColor(Color.WHITE);
                StdDraw.textLeft(1, height - 2, "Tile: " + description);

                // Also show coordinates for debugging
                StdDraw.textLeft(1, height - 3, "Pos: (" + tileX + ", " + tileY + ")");
            }
        }
    }

    /**
     * Creates a deep copy of the world tile array for rendering with entity
     * overlays.
     */
    private TETile[][] copyWorldArray(TETile[][] original) {
        TETile[][] copy = new TETile[original.length][];
        for (int x = 0; x < original.length; x++) {
            copy[x] = new TETile[original[x].length];
            System.arraycopy(original[x], 0, copy[x], 0, original[x].length);
        }
        return copy;
    }

    private class Edge {
        int roomA, roomB;
        double weight;

        Edge(int a, int b, double w) {
            this.roomA = a;
            this.roomB = b;
            this.weight = w;
        }
    }

    private class UnionFind {
        int[] parent;

        UnionFind(int n) {
            parent = new int[n];
            for (int i = 0; i < n; i++)
                parent[i] = i;
        }

        int find(int x) {
            if (parent[x] != x)
                parent[x] = find(parent[x]);
            return parent[x];
        }

        boolean union(int x, int y) {
            int rootX = find(x), rootY = find(y);
            if (rootX == rootY)
                return false;
            parent[rootX] = rootY;
            return true;
        }
    }

    public class Room {
        int x, y, width, height;

        public Room(int x, int y, int w, int h) {
            this.x = x;
            this.y = y;
            this.width = w;
            this.height = h;
        }

        public boolean intersects(Room other) {
            // Allow rooms to be very close together (minimal gap)
            return (this.x < other.x + other.width &&
                    this.x + this.width > other.x &&
                    this.y < other.y + other.height &&
                    this.y + this.height > other.y);
        }

        public int centerX() {
            return x + width / 2;
        }

        public int centerY() {
            return y + height / 2;
        }

        public Pos center() {
            return new Pos(x + width / 2, y + height / 2);
        }
    }

    @Override
    public void update(double deltaTime) {
        if (player != null) {
            this.player.updateAnimation(deltaTime);
        }

        // Update all ghost animations and movement
        for (Ghost ghost : ghosts) {
            if (ghost != null) {
                ghost.updateAnimation(deltaTime);

                // Update ghost movement
                Direction moveDirection = ghost.updateMovement(deltaTime);
                if (moveDirection != null) {
                    attemptGhostMovement(ghost, moveDirection);
                }
            }
        }
        
        // Update path visualization if enabled (recalculates paths as ghosts move)
        if (showGhostPaths && pathVisualization != null) {
            pathVisualization.updateGhosts(ghosts);
        }
    }

    @Override
    public void handleInput() {
        if (!StdDraw.hasNextKeyTyped()) {
            return;
        }

        char key = StdDraw.nextKeyTyped();
        key = Character.toLowerCase(key);

        if (colonMode && key != 'q' && key != 'Q') {
            colonMode = false; // Exit colon mode on any command key
            Log.info("Deactivate colon mode due to input key other than q/Q");
        }

        // Try to handle movement input with Player
        Direction moveDirection = player.handleMovementInput(key);
        if (moveDirection != null) {
            attemptPlayerMovement(moveDirection);
            return;
        }

        // Handle world-level commands
        switch (key) {
            case 'q':
            case 'Q':
                if (colonMode) {
                    saveAndQuit();
                } else {
                    Log.info("q/Q hit without colon mode, ignoring");
                }
                colonMode = false; // Reset colon mode after command
                break;
            case ':':
                colonMode = true;
                break;
            case '?':
                colonMode = false; // Exit colon mode
                gameEngine.setState(GameState.PAUSED);
                break;
            case 't':
                colonMode = false; // Exit colon mode
                showGhostPaths = !showGhostPaths;
                if (pathVisualization != null) {
                    pathVisualization.setEnabled(showGhostPaths);
                }
                Log.info("Ghost path visualization: " + (showGhostPaths ? "ON" : "OFF"));
                if (showGhostPaths) {
                    logGhostPaths();
                    if (pathVisualization != null) {
                        Log.info(pathVisualization.getStats());
                    }
                }
                break;
            case 'h':
            case 'H':
                colonMode = false; // Exit colon mode
                showHoverText = !showHoverText;
                Log.info("Hover text: " + (showHoverText ? "ON" : "OFF"));
                break;
            default:
                colonMode = false; // Exit colon mode
                Log.debug("Unhandled key in HubWorld: " + key);
        }
    }

    private void attemptPlayerMovement(Direction direction) {
        if (player == null || player.getPosition() == null)
            return;

        int dx = 0, dy = 0;

        // Convert direction to coordinate deltas
        switch (direction) {
            case UP:
                dy = 1;
                break;
            case DOWN:
                dy = -1;
                break;
            case LEFT:
                dx = -1;
                break;
            case RIGHT:
                dx = 1;
                break;
        }

        Pos currentPos = player.getPosition();
        int newX = currentPos.x + dx;
        int newY = currentPos.y + dy;

        // Check bounds and collision
        if (newX >= 0 && newX < world.length &&
                newY >= 0 && newY < world[0].length &&
                world[newX][newY] == Tileset.FLOOR) {

            player.setPosition(newX, newY);
            Log.debug("Player moved to: (" + newX + ", " + newY + ")");
        } else {
            Log.debug("Invalid movement attempted: (" + newX + ", " + newY + ")");
        }
    }

    private void attemptGhostMovement(Ghost ghost, Direction direction) {
        if (ghost == null || ghost.getPosition() == null)
            return;

        int dx = 0, dy = 0;

        // Convert direction to coordinate deltas
        switch (direction) {
            case UP:
                dy = 1;
                break;
            case DOWN:
                dy = -1;
                break;
            case LEFT:
                dx = -1;
                break;
            case RIGHT:
                dx = 1;
                break;
        }

        Pos currentPos = ghost.getPosition();
        int newX = currentPos.x + dx;
        int newY = currentPos.y + dy;

        // Check bounds and collision
        if (newX >= 0 && newX < world.length &&
                newY >= 0 && newY < world[0].length &&
                world[newX][newY] == Tileset.FLOOR) {

            ghost.setPosition(newX, newY);
            Log.debug("Ghost moved to: (" + newX + ", " + newY + ")");
        } else {
            Log.debug("Ghost invalid movement attempted: (" + newX + ", " + newY + ")");
        }
    }

    private void logGhostPaths() {
        Log.info("=== Ghost Movement Paths ===");
        for (int i = 0; i < ghosts.size(); i++) {
            Ghost ghost = ghosts.get(i);
            if (ghost != null) {
                List<Pos> path = ghost.getMovingPath();
                StringBuilder pathStr = new StringBuilder();
                pathStr.append(ghost.getClass().getSimpleName()).append(": ");
                for (int j = 0; j < path.size(); j++) {
                    Pos pos = path.get(j);
                    pathStr.append("(").append(pos.x).append(",").append(pos.y).append(")");
                    if (j < path.size() - 1) {
                        pathStr.append(" -> ");
                    }
                }
                Log.info(pathStr.toString());
            }
        }
        Log.info("=============================");
    }

    public boolean isShowingGhostPaths() {
        return showGhostPaths;
    }

    public boolean isShowingHoverText() {
        return showHoverText;
    }

    public List<Ghost> getGhosts() {
        return new ArrayList<>(ghosts);
    }

    private void saveAndQuit() {
        Log.info("Save and quit requested");
        // TODO: Implement save functionality
        dispose();
        System.exit(0);
    }
    
    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        Log.info("HubWorld: Theme changed to " + newTheme.getName());
        
        // First update the tileset with new theme colors
        Tileset.updateThemeTiles();
        
        // Then update world tiles to use new theme colors
        updateWorldTilesForTheme();
        
        // Update path visualization to use new theme
        if (pathVisualization != null) {
            pathVisualization.onThemeChanged(oldTheme, newTheme);
        }
        
        // Force a re-render to show new colors immediately
        render();
    }
    
    /**
     * Updates world tiles to reflect the current theme colors.
     * This recreates tiles that use theme-dependent colors.
     */
    private void updateWorldTilesForTheme() {
        if (world == null) return;
        
        // Update tiles in the world array that use theme colors
        for (int x = 0; x < world.length; x++) {
            for (int y = 0; y < world[0].length; y++) {
                TETile currentTile = world[x][y];
                
                // Check tile type by description since tile objects will be different after theme update
                if (currentTile != null) {
                    String description = currentTile.description();
                    if ("floor".equals(description)) {
                        world[x][y] = Tileset.FLOOR; // Use updated theme colors
                    } else if ("nothing".equals(description)) {
                        world[x][y] = Tileset.NOTHING; // Use updated theme colors
                    } else if ("path indicator".equals(description)) {
                        world[x][y] = Tileset.PATH_INDICATOR; // Use updated theme colors
                    }
                    // WALL tiles don't use theme colors, so they don't need updating
                }
            }
        }
    }

    @Override
    public void dispose() {
        // Clean up path visualization
        if (pathVisualization != null) {
            pathVisualization.dispose();
        }
        
        // Clean up entities
        if (player != null) {
            player.dispose();
        }
        
        for (Ghost ghost : ghosts) {
            if (ghost != null) {
                ghost.dispose();
            }
        }
        
        // Call parent dispose (removes theme listener)
        super.dispose();
    }
}
