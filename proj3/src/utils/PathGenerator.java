package utils;

import core.Pos;
import tileengine.TETile;
import tileengine.Tileset;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class PathGenerator {
    
    /**
     * Generate a random valid polygon path that avoids walls
     * @param world The world tile array to check for valid positions
     * @param seed Random seed for consistent generation
     * @param numPoints Number of points in the polygon (3-8)
     * @param minDistance Minimum distance between consecutive points
     * @param maxDistance Maximum distance between consecutive points
     * @return List of positions forming a valid polygon path
     */
    public static List<Pos> generateRandomPolygonPath(TETile[][] world, long seed, int numPoints, 
                                                      int minDistance, int maxDistance) {
        Random rand = new Random(seed);
        List<Pos> validFloorPositions = findAllFloorPositions(world);
        
        if (validFloorPositions.isEmpty()) {
            Log.debug("Warning: No floor positions found for path generation");
            return new ArrayList<>();
        }
        
        // Clamp numPoints to reasonable range
        numPoints = Math.max(3, Math.min(8, numPoints));
        
        List<Pos> path = new ArrayList<>();
        
        // Pick a random starting point
        Pos startPoint = validFloorPositions.get(rand.nextInt(validFloorPositions.size()));
        path.add(startPoint);
        
        // Generate remaining points
        for (int i = 1; i < numPoints; i++) {
            Pos newPoint = findValidNextPoint(world, path, rand, validFloorPositions, 
                                              minDistance, maxDistance);
            if (newPoint != null) {
                path.add(newPoint);
            } else {
                // If we can't find a valid next point, break and return what we have
                Log.debug("Could not find valid point " + (i + 1) + " for polygon, using " + path.size() + " points");
                break;
            }
        }
        
        // Ensure we have at least 3 points for a valid polygon
        if (path.size() < 3) {
            Log.debug("Warning: Generated path has fewer than 3 points, creating fallback triangle");
            return createFallbackTrianglePath(validFloorPositions, rand);
        }
        
        return path;
    }
    
    /**
     * Find all floor positions in the world
     */
    private static List<Pos> findAllFloorPositions(TETile[][] world) {
        List<Pos> floorPositions = new ArrayList<>();
        for (int x = 0; x < world.length; x++) {
            for (int y = 0; y < world[0].length; y++) {
                if (world[x][y] == Tileset.FLOOR) {
                    floorPositions.add(new Pos(x, y));
                }
            }
        }
        return floorPositions;
    }
    
    /**
     * Find a valid next point for the polygon that maintains distance constraints
     */
    private static Pos findValidNextPoint(TETile[][] world, List<Pos> currentPath, Random rand,
                                          List<Pos> validFloorPositions, int minDistance, int maxDistance) {
        Pos lastPoint = currentPath.get(currentPath.size() - 1);
        List<Pos> candidates = new ArrayList<>();
        
        // Find all floor positions within distance range
        for (Pos candidate : validFloorPositions) {
            int distance = manhattanDistance(lastPoint, candidate);
            
            // Check distance constraints
            if (distance >= minDistance && distance <= maxDistance) {
                // Check if path from last point to candidate is valid
                if (isPathClear(world, lastPoint, candidate)) {
                    // Make sure this point doesn't make the polygon too irregular
                    if (isGoodPolygonPoint(currentPath, candidate)) {
                        candidates.add(candidate);
                    }
                }
            }
        }
        
        if (candidates.isEmpty()) {
            return null;
        }
        
        return candidates.get(rand.nextInt(candidates.size()));
    }
    
    /**
     * Check if the straight-line path between two points is clear of walls
     */
    private static boolean isPathClear(TETile[][] world, Pos from, Pos to) {
        // Use Bresenham's line algorithm to check all points along the path
        int x0 = from.x, y0 = from.y;
        int x1 = to.x, y1 = to.y;
        
        int dx = Math.abs(x1 - x0);
        int dy = Math.abs(y1 - y0);
        int sx = x0 < x1 ? 1 : -1;
        int sy = y0 < y1 ? 1 : -1;
        int err = dx - dy;
        
        int x = x0, y = y0;
        
        while (true) {
            // Check if current position is valid
            if (x < 0 || x >= world.length || y < 0 || y >= world[0].length) {
                return false;
            }
            if (world[x][y] != Tileset.FLOOR) {
                return false;
            }
            
            if (x == x1 && y == y1) break;
            
            int e2 = 2 * err;
            if (e2 > -dy) {
                err -= dy;
                x += sx;
            }
            if (e2 < dx) {
                err += dx;
                y += sy;
            }
        }
        
        return true;
    }
    
    /**
     * Check if a candidate point would make a reasonable polygon shape
     */
    private static boolean isGoodPolygonPoint(List<Pos> currentPath, Pos candidate) {
        if (currentPath.size() < 2) return true;
        
        // Avoid points that are too close to existing points (except the last one)
        for (int i = 0; i < currentPath.size() - 1; i++) {
            if (manhattanDistance(currentPath.get(i), candidate) < 2) {
                return false;
            }
        }
        
        // Avoid creating very sharp angles if we have enough points
        if (currentPath.size() >= 2) {
            Pos prev2 = currentPath.get(currentPath.size() - 2);
            Pos prev1 = currentPath.get(currentPath.size() - 1);
            
            // Simple angle check: avoid points that create very sharp turns
            int dx1 = prev1.x - prev2.x;
            int dy1 = prev1.y - prev2.y;
            int dx2 = candidate.x - prev1.x;
            int dy2 = candidate.y - prev1.y;
            
            // If the direction change is too sharp (opposite directions), reject
            if (dx1 * dx2 + dy1 * dy2 < -0.5 * Math.sqrt((dx1*dx1 + dy1*dy1) * (dx2*dx2 + dy2*dy2))) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Calculate Manhattan distance between two points
     */
    private static int manhattanDistance(Pos a, Pos b) {
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
    }
    
    /**
     * Create a fallback triangle path if polygon generation fails
     */
    private static List<Pos> createFallbackTrianglePath(List<Pos> validFloorPositions, Random rand) {
        List<Pos> fallbackPath = new ArrayList<>();
        
        if (validFloorPositions.size() >= 3) {
            // Pick 3 random points
            for (int i = 0; i < 3; i++) {
                Pos point = validFloorPositions.get(rand.nextInt(validFloorPositions.size()));
                // Make sure we don't pick the same point twice
                while (fallbackPath.contains(point) && validFloorPositions.size() > fallbackPath.size()) {
                    point = validFloorPositions.get(rand.nextInt(validFloorPositions.size()));
                }
                fallbackPath.add(point);
            }
        }
        
        return fallbackPath;
    }
}