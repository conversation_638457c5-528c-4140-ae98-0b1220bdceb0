package utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Centralized logging utility class providing a simple interface for application-wide logging.
 * <p>
 * This class wraps Log4j2 functionality and provides static methods for different log levels.
 * All logging operations are performed through a single named logger instance.
 * </p>
 *
 * <p>Usage examples:</p>
 * <pre>
 * Log.info("Application started");
 * Log.debug("Processing user request for ID: {}", userId);
 * Log.warn("Low memory warning: {} MB remaining", memoryMB);
 * Log.error("Database connection failed", exception);
 * </pre>
 */
public class Log {

    /**
     * The underlying Log4j2 logger instance used for all logging operations.
     * Named "MyApp" to distinguish from other loggers in the application.
     */
    private static final Logger logger = LogManager.getLogger("MyApp");

    /**
     * Logs a message at DEBUG level.
     * <p>
     * Debug messages are typically used for detailed diagnostic information
     * that is only of interest when diagnosing problems.
     * </p>
     *
     * @param message the message to log; can contain placeholders like {}
     * @param args    optional arguments to substitute into the message placeholders
     */
    public static void debug(String message, Object... args) {
        logger.debug(message, args);
    }

    /**
     * Logs a message at INFO level.
     * <p>
     * Info messages are used for informational messages that highlight
     * the progress of the application at a coarse-grained level.
     * </p>
     *
     * @param message the message to log; can contain placeholders like {}
     * @param args    optional arguments to substitute into the message placeholders
     */
    public static void info(String message, Object... args) {
        logger.info(message, args);
    }

    /**
     * Logs a message at WARN level.
     * <p>
     * Warn messages are used for potentially harmful situations that
     * should be brought to attention but don't prevent the application from continuing.
     * </p>
     *
     * @param message the message to log; can contain placeholders like {}
     * @param args    optional arguments to substitute into the message placeholders
     */
    public static void warn(String message, Object... args) {
        logger.warn(message, args);
    }

    /**
     * Logs a message at ERROR level.
     * <p>
     * Error messages are used for error events that might still allow
     * the application to continue running.
     * </p>
     *
     * @param message the message to log; can contain placeholders like {}
     * @param args    optional arguments to substitute into the message placeholders
     */
    public static void error(String message, Object... args) {
        logger.error(message, args);
    }

    /**
     * Logs a message at ERROR level with an associated exception.
     * <p>
     * This method should be used when logging errors that are caused by
     * exceptions. The exception stack trace will be included in the log output.
     * </p>
     *
     * @param message the message to log describing the error context
     * @param e       the exception that caused the error
     */
    public static void error(String message, Throwable e) {
        logger.error(message, e);
    }
}