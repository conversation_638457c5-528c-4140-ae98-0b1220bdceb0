package utils;

import core.Pos;
import tileengine.TETile;
import tileengine.Tileset;
import java.util.*;

public class AStar {
    
    /**
     * Node class for A* pathfinding
     */
    public static class Node implements Comparable<Node> {
        public Pos pos;
        public double gCost; // Distance from start
        public double hCost; // Heuristic distance to goal
        public double fCost; // Total cost (g + h)
        public Node parent;
        
        public Node(Pos pos, double gCost, double hCost, Node parent) {
            this.pos = pos;
            this.gCost = gCost;
            this.hCost = hCost;
            this.fCost = gCost + hCost;
            this.parent = parent;
        }
        
        @Override
        public int compareTo(Node other) {
            return Double.compare(this.fCost, other.fCost);
        }
        
        @Override
        public boolean equals(Object obj) {
            if (obj instanceof Node) {
                Node other = (Node) obj;
                return pos.x == other.pos.x && pos.y == other.pos.y;
            }
            return false;
        }
        
        @Override
        public int hashCode() {
            return pos.x * 1000 + pos.y;
        }
    }
    
    /**
     * Find the shortest path from start to goal using A* algorithm
     * @param world The world tile array
     * @param start Starting position
     * @param goal Goal position
     * @return List of positions representing the path (excluding start, including goal)
     *         Returns empty list if no path found
     */
    public static List<Pos> findPath(TETile[][] world, Pos start, Pos goal) {
        if (start.x == goal.x && start.y == goal.y) {
            return new ArrayList<>(); // Already at goal
        }
        
        // Check if goal is reachable
        if (!isValidPosition(world, goal)) {
            return new ArrayList<>();
        }
        
        PriorityQueue<Node> openSet = new PriorityQueue<>();
        Set<Node> closedSet = new HashSet<>();
        
        Node startNode = new Node(start, 0, manhattanDistance(start, goal), null);
        openSet.add(startNode);
        
        while (!openSet.isEmpty()) {
            Node current = openSet.poll();
            closedSet.add(current);
            
            // Check if we reached the goal
            if (current.pos.x == goal.x && current.pos.y == goal.y) {
                return reconstructPath(current);
            }
            
            // Explore neighbors
            for (Pos neighbor : getNeighbors(world, current.pos)) {
                Node neighborNode = new Node(neighbor, 0, 0, current);
                
                // Skip if already evaluated
                if (closedSet.contains(neighborNode)) {
                    continue;
                }
                
                double tentativeGCost = current.gCost + getMoveCost(current.pos, neighbor);
                
                // Check if this node is already in open set with better cost
                Node existingNode = findNodeInSet(openSet, neighbor);
                if (existingNode != null && tentativeGCost >= existingNode.gCost) {
                    continue;
                }
                
                // Remove existing node with worse cost
                if (existingNode != null) {
                    openSet.remove(existingNode);
                }
                
                // Add new node with better cost
                neighborNode.gCost = tentativeGCost;
                neighborNode.hCost = manhattanDistance(neighbor, goal);
                neighborNode.fCost = neighborNode.gCost + neighborNode.hCost;
                openSet.add(neighborNode);
            }
        }
        
        // No path found
        return new ArrayList<>();
    }
    
    /**
     * Get valid neighboring positions
     */
    private static List<Pos> getNeighbors(TETile[][] world, Pos pos) {
        List<Pos> neighbors = new ArrayList<>();
        
        // Check 4-directional movement (up, down, left, right)
        int[] dx = {0, 0, -1, 1};
        int[] dy = {1, -1, 0, 0};
        
        for (int i = 0; i < 4; i++) {
            Pos neighbor = new Pos(pos.x + dx[i], pos.y + dy[i]);
            if (isValidPosition(world, neighbor)) {
                neighbors.add(neighbor);
            }
        }
        
        return neighbors;
    }
    
    /**
     * Check if a position is valid for movement
     */
    private static boolean isValidPosition(TETile[][] world, Pos pos) {
        if (pos.x < 0 || pos.x >= world.length || pos.y < 0 || pos.y >= world[0].length) {
            return false;
        }
        
        TETile tile = world[pos.x][pos.y];
        return tile == Tileset.FLOOR; // Only floor tiles are walkable
    }
    
    /**
     * Calculate Manhattan distance between two positions
     */
    private static double manhattanDistance(Pos a, Pos b) {
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y);
    }
    
    /**
     * Get the cost of moving from one position to another
     * (uniform cost for now, but can be extended for different terrain costs)
     */
    private static double getMoveCost(Pos from, Pos to) {
        return 1.0; // Uniform cost for all moves
    }
    
    /**
     * Find a node in the set by position
     */
    private static Node findNodeInSet(Collection<Node> set, Pos pos) {
        for (Node node : set) {
            if (node.pos.x == pos.x && node.pos.y == pos.y) {
                return node;
            }
        }
        return null;
    }
    
    /**
     * Reconstruct the path from goal to start by following parent pointers
     */
    private static List<Pos> reconstructPath(Node goalNode) {
        List<Pos> path = new ArrayList<>();
        Node current = goalNode;
        
        while (current.parent != null) {
            path.add(current.pos);
            current = current.parent;
        }
        
        // Reverse to get path from start to goal
        Collections.reverse(path);
        return path;
    }
}