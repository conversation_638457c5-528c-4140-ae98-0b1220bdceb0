package tileengine;

import java.awt.Color;

import ui.SpriteManager.GhostType;
import ui.themes.ColorKey;
import ui.themes.ThemeManager;

/**
 * Contains constant tile objects, to avoid having to remake the same tiles in
 * different parts of
 * the code.
 *
 * You are free to (and encouraged to) create and add your own tiles to this
 * file. This file will
 * be turned in with the rest of your code.
 *
 * Ex:
 * world[x][y] = Tileset.FLOOR;
 *
 * The style checker may crash when you try to style check this file due to use
 * of unicode
 * characters. This is OK.
 */

public class Tileset {
    private static final ThemeManager themeManager = ThemeManager.getInstance();
    
    // Static tiles that don't change with themes
    public static final TETile AVATAR = new TETile('@', Color.white, Color.black, "you", 0);
    public static final TETile WALL = new TETile('#', new Color(216, 128, 128), Color.darkGray, "wall", 1);
    
    // Dynamic tiles that update with theme changes
    public static TETile FLOOR = createFloorTile();
    public static TETile NOTHING = createNothingTile();
    public static TETile PATH_INDICATOR = createPathIndicatorTile();
    
    /**
     * Update all theme-dependent tiles with current theme colors.
     * This should be called when the theme changes.
     */
    public static void updateThemeTiles() {
        FLOOR = createFloorTile();
        NOTHING = createNothingTile();
        PATH_INDICATOR = createPathIndicatorTile();
    }
    
    private static TETile createFloorTile() {
        return new TETile('·', new Color(128, 192, 128), themeManager.getColor(ColorKey.FLOOR), "floor", 2);
    }
    
    private static TETile createNothingTile() {
        return new TETile(' ', Color.black, themeManager.getColor(ColorKey.BACKGROUND), "nothing", 3);
    }
    
    private static TETile createPathIndicatorTile() {
        return new TETile('o', themeManager.getColor(ColorKey.WHITE), themeManager.getColor(ColorKey.BLACK), "path indicator", 4);
    }

    /**
     * Create a path indicator tile for a specific ghost type with matching color
     */
    public static TETile createGhostPathTile(GhostType ghostType) {
        Color ghostColor = ghostType.getColor();
        Color backgroundColor = themeManager.getColor(ColorKey.BLACK);
        return new TETile('o', ghostColor, backgroundColor,
                         "path indicator for " + ghostType.name().toLowerCase() + " ghost", 4);
    }
}
