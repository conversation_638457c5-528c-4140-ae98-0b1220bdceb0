package config;

import ui.themes.ThemeManager.ThemeType;

public class GameConfig {
    public static final ThemeType DEFAULT_THEME_TYPE = ThemeType.MONOKAI;

    public static final int TILE_SIZE = 16; // Size of each tile in pixels

    public static final long DEFAULT_SEED = 42L; // Default seed value for world generation

    public static final int WIDTH = 50; // Width of the game world in tiles
    public static final int HEIGHT = 50; // Height of the game world in tiles

    public static final int TARGET_FPS = 30; // Frames per second for game loop
    public static final int UPDATE_RATE_MS = 1000 / TARGET_FPS;

    public static final String ASSETS_PATH = "assets/"; // Base path for game assets
    public static final String SPRITE_PATH = ASSETS_PATH + "sprites/"; // Path

    public static final int BASE_GHOST_SPEED = 1;

    public static final double PLAYER_ANIMATION_SPEED = 0.2; // Time between player animation frames in seconds
    public static final double GHOST_ANIMATION_SPEED = 0.2; // Time between ghost animation frames in seconds
    
    public static final int GHOST_COUNT = 4; // Number of ghosts to spawn in the world
    
    // Ghost AI configuration
    public static final double RED_GHOST_PATH_REFRESH_INTERVAL = 2.0; // Seconds between A* recalculations
    public static final double RED_GHOST_MOVE_SPEED = 0.3; // Time between moves for Red Ghost

}
