package ui;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import tileengine.TETile;
import ui.themes.ColorKey;
import ui.themes.Theme;
import ui.themes.ThemeManager;
import ui.themes.ThemeManager.ThemeListener;

public class SpriteManager implements ThemeListener {
    private static SpriteManager instance;
    private ThemeManager themeManager;
    
    // Cache for avatar tiles by direction
    private Map<Direction, TETile[]> avatarTilesCache;
    
    // Directory for generated sprite files
    private static final String TEMP_SPRITE_DIR = "temp/sprites/";
    private static final String BASE_SPRITE_DIR = "assets/sprites/";
    
    public enum Direction {
        UP, DOWN, LEFT, RIGHT;

        public char getSymbol() {
            return switch (this) {
                case UP -> '^';
                case DOWN -> 'v';
                case LEFT -> '<';
                case RIGHT -> '>';
            };
        }
    }
    
    public enum GhostType {
        RED, PINK, BLUE, ORANGE;
        
        public Color getColor() {
            return switch (this) {
                case RED -> Color.RED;
                case PINK -> Color.PINK;
                case BLUE -> Color.BLUE;
                case ORANGE -> Color.ORANGE;
            };
        }
    }
    
    private SpriteManager() {
        this.themeManager = ThemeManager.getInstance();
        this.avatarTilesCache = new HashMap<>();
        
        // Register as theme listener for automatic regeneration
        themeManager.addThemeListener(this);
        
        // Initialize cache with themed tiles
        generateAndCacheSprites();
    }
    
    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        // Clear cache and regenerate sprites when theme changes
        clearCache();
        generateAndCacheSprites();
    }
    
    private void clearCache() {
        avatarTilesCache.clear();
        // Clean up temp sprite files
        cleanupTempSprites();
    }
    
    private void generateAndCacheSprites() {
        try {
            // Ensure temp directory exists
            createTempDirectory();
            
            Color avatarColor = themeManager.getColor(ColorKey.PLAYER);
            Color backgroundColor = themeManager.getColor(ColorKey.BACKGROUND);
            
            // Generate sprites for each direction
            for (Direction direction : Direction.values()) {
                TETile[] frames = new TETile[4];
                char symbol = direction.getSymbol();
                
                for (int i = 0; i < 4; i++) {
                    String generatedSpritePath = generateThemedSprite(direction, i, avatarColor);
                    frames[i] = new TETile(symbol, avatarColor, backgroundColor, 
                        "player_" + direction.name().toLowerCase() + "_frame_" + i, 
                        generatedSpritePath, i);
                }
                
                avatarTilesCache.put(direction, frames);
            }
        } catch (IOException e) {
            System.err.println("Error generating sprites: " + e.getMessage());
            // Fallback to basic tiles without images
            generateBasicTiles();
        }
    }
    
    private void generateBasicTiles() {
        Color avatarColor = themeManager.getColor(ColorKey.PLAYER);
        Color backgroundColor = themeManager.getColor(ColorKey.BACKGROUND);
        
        for (Direction direction : Direction.values()) {
            TETile[] frames = new TETile[4];
            char symbol = direction.getSymbol();
            
            for (int i = 0; i < 4; i++) {
                frames[i] = new TETile(symbol, avatarColor, backgroundColor, 
                    "player_" + direction.name().toLowerCase() + "_frame_" + i, i);
            }
            
            avatarTilesCache.put(direction, frames);
        }
    }
    
    public static SpriteManager getInstance() {
        if (instance == null) {
            instance = new SpriteManager();
        }
        return instance;
    }
    
    /**
     * Gets all 4 avatar tiles for animation frames in the specified direction.
     * Uses current theme colors for the avatar.
     */
    public TETile[] getAvatarTiles(Direction direction) {
        return avatarTilesCache.get(direction);
    }
    
    /**
     * Gets ghost tiles for animation frames in the specified direction.
     * TODO: Implement proper ghost sprite generation with themed colors and transformations
     */
    public TETile[] getGhostTiles(GhostType ghostType, Direction direction) {
        switch (ghostType) {
            case RED:
                return new TETile[]{new TETile('R', Color.RED, Color.BLACK, "red_ghost", 0)};
            case PINK:
                return new TETile[]{new TETile('P', Color.PINK, Color.BLACK, "pink_ghost", 0)};
            case BLUE:
                return new TETile[]{new TETile('B', Color.BLUE, Color.BLACK, "blue_ghost", 0)};
            case ORANGE:
                return new TETile[]{new TETile('O', Color.ORANGE, Color.BLACK, "orange_ghost", 0)};
            default:
                break;
        }
        throw new UnsupportedOperationException("Ghost sprite generation not implemented yet");
    }
    
    private void createTempDirectory() throws IOException {
        File tempDir = new File(TEMP_SPRITE_DIR);
        if (!tempDir.exists()) {
            boolean created = tempDir.mkdirs();
            if (!created) {
                throw new IOException("Failed to create temp sprite directory: " + TEMP_SPRITE_DIR);
            }
        }
    }
    
    private void cleanupTempSprites() {
        File tempDir = new File(TEMP_SPRITE_DIR);
        if (tempDir.exists() && tempDir.isDirectory()) {
            File[] files = tempDir.listFiles((dir, name) -> name.startsWith("avatar_"));
            if (files != null) {
                for (File file : files) {
                    file.delete();
                }
            }
        }
    }
    
    private String generateThemedSprite(Direction direction, int frame, Color avatarColor) throws IOException {
        // Load base sprite
        String baseSpritePath = BASE_SPRITE_DIR + "avatar_" + frame + ".png";
        File baseSpriteFile = new File(baseSpritePath);
        
        if (!baseSpriteFile.exists()) {
            throw new IOException("Base sprite not found: " + baseSpritePath);
        }
        
        BufferedImage baseImage = ImageIO.read(baseSpriteFile);
        
        // Apply theme color
        BufferedImage themedImage = applyThemeColor(baseImage, avatarColor);
        
        // Transform for direction
        BufferedImage transformedImage = transformSpriteForDirection(themedImage, direction);
        
        // Save to temp directory
        String filename = "avatar_" + direction.name().toLowerCase() + "_" + frame + "_" + 
                         themeManager.getCurrentTheme().getName().toLowerCase() + ".png";
        String outputPath = TEMP_SPRITE_DIR + filename;
        
        ImageIO.write(transformedImage, "PNG", new File(outputPath));
        
        return outputPath;
    }
    
    private BufferedImage applyThemeColor(BufferedImage original, Color newColor) {
        BufferedImage result = new BufferedImage(original.getWidth(), original.getHeight(), 
                                                BufferedImage.TYPE_INT_ARGB);
        
        for (int x = 0; x < original.getWidth(); x++) {
            for (int y = 0; y < original.getHeight(); y++) {
                int pixel = original.getRGB(x, y);
                int alpha = (pixel >> 24) & 0xFF;
                
                if (alpha > 0) { // Non-transparent pixel
                    // Replace yellow pixels with theme color, keep others as-is
                    int red = (pixel >> 16) & 0xFF;
                    int green = (pixel >> 8) & 0xFF;
                    int blue = pixel & 0xFF;
                    
                    // Check if pixel is yellowish (original avatar color)
                    if (red > 200 && green > 200 && blue < 100) {
                        int newRGB = (alpha << 24) | (newColor.getRed() << 16) | 
                                   (newColor.getGreen() << 8) | newColor.getBlue();
                        result.setRGB(x, y, newRGB);
                    } else {
                        result.setRGB(x, y, pixel);
                    }
                } else {
                    result.setRGB(x, y, pixel);
                }
            }
        }
        
        return result;
    }
    
    private BufferedImage transformSpriteForDirection(BufferedImage image, Direction direction) {
        return switch (direction) {
            case RIGHT -> image; // No transformation needed
            case LEFT -> flipHorizontally(image);
            case UP -> rotateImage(image, -90);
            case DOWN -> rotateImage(image, 90);
        };
    }
    
    private BufferedImage flipHorizontally(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage flipped = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = flipped.createGraphics();
        g.drawImage(image, width, 0, 0, height, 0, 0, width, height, null);
        g.dispose();
        return flipped;
    }
    
    private BufferedImage rotateImage(BufferedImage image, double degrees) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage rotated = new BufferedImage(height, width, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = rotated.createGraphics();
        
        AffineTransform transform = new AffineTransform();
        transform.translate(height / 2.0, width / 2.0);
        transform.rotate(Math.toRadians(degrees));
        transform.translate(-width / 2.0, -height / 2.0);
        
        g.setTransform(transform);
        g.drawImage(image, 0, 0, null);
        g.dispose();
        
        return rotated;
    }
}
