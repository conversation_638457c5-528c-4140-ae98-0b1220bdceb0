package ui;

import core.Pos;
import core.Renderable;
import entity.Ghost;
import tileengine.TETile;
import tileengine.Tileset;
import ui.themes.Theme;
import ui.themes.ThemeManager;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Visualization system for ghost movement paths.
 * Renders path indicators on the world to show where ghosts will move.
 */
public class PathVisualization implements Renderable {
    
    private List<Ghost> ghosts;
    private boolean enabled;
    private Set<Pos> pathPositions;
    private TETile pathTile;
    
    public PathVisualization(List<Ghost> ghosts) {
        this.ghosts = new ArrayList<>(ghosts);
        this.enabled = false;
        this.pathPositions = new HashSet<>();
        this.pathTile = Tileset.PATH_INDICATOR;
        
        // Register for theme updates
        ThemeManager.getInstance().addThemeListener(this);
        
        // Initial path calculation
        updatePathVisualization();
    }
    
    /**
     * Enable or disable path visualization
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        if (enabled) {
            updatePathVisualization();
        } else {
            pathPositions.clear();
        }
    }
    
    /**
     * Toggle path visualization on/off
     */
    public void toggle() {
        setEnabled(!enabled);
    }
    
    /**
     * Check if path visualization is enabled
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Update the list of ghosts to visualize
     */
    public void updateGhosts(List<Ghost> ghosts) {
        this.ghosts = new ArrayList<>(ghosts);
        if (enabled) {
            updatePathVisualization();
        }
    }
    
    /**
     * Recalculate path visualization based on current ghost positions and strategies
     */
    public void updatePathVisualization() {
        pathPositions.clear();
        
        if (!enabled) {
            return;
        }
        
        for (Ghost ghost : ghosts) {
            if (ghost != null) {
                List<Pos> ghostPath = ghost.getMovingPath();
                if (ghostPath != null) {
                    // Add all path positions, avoiding duplicates with Set
                    pathPositions.addAll(ghostPath);
                }
            }
        }
    }
    
    /**
     * Get all positions where path indicators should be rendered
     */
    public Set<Pos> getPathPositions() {
        return new HashSet<>(pathPositions);
    }
    
    /**
     * Check if a position should show a path indicator
     */
    public boolean hasPathIndicator(int x, int y) {
        return enabled && pathPositions.contains(new Pos(x, y));
    }
    
    /**
     * Check if a position should show a path indicator
     */
    public boolean hasPathIndicator(Pos pos) {
        return enabled && pathPositions.contains(pos);
    }
    
    /**
     * Get the tile to use for path indicators
     */
    public TETile getPathTile() {
        return pathTile;
    }
    
    /**
     * Render path indicators to the world array
     * This modifies the world array directly for path visualization
     */
    public void renderToWorld(TETile[][] world) {
        if (!enabled || world == null) {
            return;
        }
        
        for (Pos pos : pathPositions) {
            if (pos.x >= 0 && pos.x < world.length && 
                pos.y >= 0 && pos.y < world[0].length) {
                
                // Only render path indicators on floor tiles (don't overwrite walls/entities)
                if (world[pos.x][pos.y] == Tileset.FLOOR) {
                    world[pos.x][pos.y] = pathTile;
                }
            }
        }
    }
    
    /**
     * Render path indicators at their positions.
     * This is the Renderable interface implementation.
     */
    @Override
    public void render() {
        if (!enabled) {
            return;
        }
        
        // Render each path indicator at its position
        for (Pos pos : pathPositions) {
            pathTile.draw(pos.x, pos.y);
        }
    }
    
    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        // Update path tile to use new theme colors
        this.pathTile = Tileset.PATH_INDICATOR;
    }
    
    /**
     * Get statistics about the current path visualization
     */
    public String getStats() {
        if (!enabled) {
            return "Path visualization: OFF";
        }
        
        return String.format("Path visualization: ON (%d positions, %d ghosts)", 
                           pathPositions.size(), ghosts.size());
    }
    
    /**
     * Clear all path data
     */
    public void clear() {
        pathPositions.clear();
    }
}