package ui.themes;

import java.awt.Color;

/**
 * Monokai theme implementation with classic dark theme colors.
 */
public class MonokaiTheme extends Theme {
    public MonokaiTheme() {
        super("Monokai", "Classic dark theme with vibrant colors");
    }
    
    @Override
    protected void initializeColors() {
        // Basic Colors
        colors.put(ColorKey.BACKGROUND, new Color(0x272822));
        colors.put(ColorKey.FOREGROUND, new Color(0xF8F8F2));
        colors.put(ColorKey.PRIMARY, new Color(0xF92672));
        colors.put(ColorKey.SECONDARY, new Color(0x75715E));
        colors.put(ColorKey.ACCENT, new Color(0xFD971F));
        colors.put(ColorKey.WHITE, new Color(0xF8F8F2));
        colors.put(ColorKey.BLACK, new Color(0x272822));
        colors.put(ColorKey.RED, new Color(0xF92672));
        colors.put(ColorKey.ORANGE, new Color(0xFD971F));
        colors.put(ColorKey.BLUE, new Color(0x66D9EF));
        colors.put(ColorKey.PINK, new Color(0xF92672));
        colors.put(ColorKey.YELLOW, new Color(0xE6DB74));
        
        // UI element colors
        colors.put(ColorKey.TEXT, new Color(0xF8F8F2));
        colors.put(ColorKey.TEXT_SECONDARY, new Color(0x75715E));
        colors.put(ColorKey.BORDER, new Color(0x49483E));
        colors.put(ColorKey.BUTTON_BG, new Color(0x49483E));
        colors.put(ColorKey.BUTTON_FG, new Color(0xF8F8F2));
        colors.put(ColorKey.MENU_BG, new Color(0x272822));
        colors.put(ColorKey.MENU_FG, new Color(0xF8F8F2));
        
        // Game element colors
        colors.put(ColorKey.WALL, new Color(0x66D9EF));
        colors.put(ColorKey.FLOOR, new Color(0x272822));
        colors.put(ColorKey.PLAYER, new Color(0xFD971F));
        colors.put(ColorKey.GHOST, new Color(0xF92672));
        colors.put(ColorKey.PELLET, new Color(0xF8F8F2));
        colors.put(ColorKey.POWER_PELLET, new Color(0xA6E22E));
    }
}
