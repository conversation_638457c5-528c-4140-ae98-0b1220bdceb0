package ui.themes;

import java.awt.Color;

/**
 * Tokyo Night theme implementation with modern dark theme colors.
 */
public class TokyoNightTheme extends Theme {
    public TokyoNightTheme() {
        super("Tokyo Night", "Modern dark theme with blue accents");
    }
    
    @Override
    protected void initializeColors() {
        // Basic Colors
        colors.put(ColorKey.BACKGROUND, new Color(0x1a1b26));
        colors.put(ColorKey.FOREGROUND, new Color(0xc0caf5));
        colors.put(ColorKey.PRIMARY, new Color(0x7aa2f7));
        colors.put(ColorKey.SECONDARY, new Color(0x565f89));
        colors.put(ColorKey.ACCENT, new Color(0xf7768e));
        colors.put(ColorKey.WHITE, new Color(0xc0caf5));
        colors.put(ColorKey.BLACK, new Color(0x1a1b26));
        colors.put(ColorKey.RED, new Color(0xf7768e));
        colors.put(ColorKey.ORANGE, new Color(0xff9e64));
        colors.put(ColorKey.BLUE, new Color(0x7aa2f7));
        colors.put(ColorKey.PINK, new Color(0xf7768e));
        colors.put(ColorKey.YELLOW, new Color(0xe0af68));
        
        // UI element colors
        colors.put(ColorKey.TEXT, new Color(0xc0caf5));
        colors.put(ColorKey.TEXT_SECONDARY, new Color(0x565f89));
        colors.put(ColorKey.BORDER, new Color(0x414868));
        colors.put(ColorKey.BUTTON_BG, new Color(0x414868));
        colors.put(ColorKey.BUTTON_FG, new Color(0xc0caf5));
        colors.put(ColorKey.MENU_BG, new Color(0x1a1b26));
        colors.put(ColorKey.MENU_FG, new Color(0xc0caf5));
        
        // Game element colors
        colors.put(ColorKey.WALL, new Color(0x7aa2f7));
        colors.put(ColorKey.FLOOR, new Color(0x1a1b26));
        colors.put(ColorKey.PLAYER, new Color(0xe0af68));
        colors.put(ColorKey.GHOST, new Color(0xf7768e));
        colors.put(ColorKey.PELLET, new Color(0xc0caf5));
        colors.put(ColorKey.POWER_PELLET, new Color(0x9ece6a));
    }
}
