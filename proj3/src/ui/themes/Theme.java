package ui.themes;

import java.awt.Color;
import java.util.EnumMap;
import java.util.Map;

/**
 * Abstract base class for themes in the Pac-Man game.
 * Provides a framework for defining color schemes and theme properties.
 */
public abstract class Theme {
    protected final String name;
    protected final String description;
    protected final Map<ColorKey, Color> colors;

    public Theme(String name, String description) {
        this.name = name;
        this.description = description;
        this.colors = new EnumMap<>(ColorKey.class);
        initializeColors();
    }

    /**
     * Subclasses implement specific color initialization
     */
    protected abstract void initializeColors();

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Color getColor(ColorKey key) {
        return colors.getOrDefault(key, getDefaultColor(key));
    }

    /**
     * Provides default color fallback mechanism
     */
    protected Color getDefaultColor(ColorKey key) {
        return switch (key) {
            // Basic Colors
            case BACKGROUND -> Color.WHITE;
            case FOREGROUND -> Color.BLACK;
            case PRIMARY -> Color.BLUE;
            case SECONDARY -> Color.GRAY;
            case ACCENT -> Color.RED;
            case WHITE -> Color.WHITE;
            case BLACK -> Color.BLACK;
            case RED -> Color.RED;
            case ORANGE -> Color.ORANGE;
            case BLUE -> Color.BLUE;
            case PINK -> Color.PINK;
            case YELLOW -> Color.YELLOW;

            // UI elements
            case TEXT -> Color.BLACK;
            case TEXT_SECONDARY -> Color.DARK_GRAY;
            case BORDER -> Color.GRAY;
            case BUTTON_BG -> Color.LIGHT_GRAY;
            case BUTTON_FG -> Color.BLACK;
            case MENU_BG -> Color.WHITE;
            case MENU_FG -> Color.BLACK;

            // Game elements
            case WALL -> Color.BLUE;
            case FLOOR -> Color.LIGHT_GRAY;
            case PLAYER -> Color.YELLOW;
            case GHOST -> Color.RED;
            case PELLET -> Color.WHITE;
            case POWER_PELLET -> Color.YELLOW;
        };
    }

    public Map<ColorKey, Color> getAllColors() {
        return new EnumMap<>(colors);
    }

    /**
     * Displays a simple color palette
     */
    public void checkColors() {
        System.out.println(name + " Theme Colors:");
        System.out.println("─".repeat(30));
        
        boolean hasErrors = false;

        for (ColorKey colorKey : ColorKey.values()) {
            if (colors.containsKey(colorKey)) {
                Color color = colors.get(colorKey);
                // Create a color block using ANSI escape codes
                String colorBlock = String.format("\u001B[48;2;%d;%d;%dm   \u001B[0m", 
                    color.getRed(), color.getGreen(), color.getBlue());
                String hex = String.format("#%06X", color.getRGB() & 0xFFFFFF);
                
                System.out.printf("%s %-15s %s%n", colorBlock, colorKey.getKey(), hex);
            } else {
                System.out.printf("%-15s MISSING%n", colorKey.getKey());
                hasErrors = true;
            }
        }
        
        System.out.println("─".repeat(30));
        if (!hasErrors) {
            System.out.println("All colors defined");
        } else {
            System.out.println("Some colors missing");
        }
    }
}