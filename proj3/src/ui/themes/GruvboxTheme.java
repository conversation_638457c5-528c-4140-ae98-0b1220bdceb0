package ui.themes;

import java.awt.Color;

/**
 * Gruvbox theme implementation with retro dark theme colors.
 */
public class GruvboxTheme extends Theme {
    public GruvboxTheme() {
        super("Gruvbox", "Retro dark theme with warm colors");
    }
    
    @Override
    protected void initializeColors() {
        // Basic Colors
        colors.put(ColorKey.BACKGROUND, new Color(0x282828));
        colors.put(ColorKey.FOREGROUND, new Color(0xebdbb2));
        colors.put(ColorKey.PRIMARY, new Color(0x458588));
        colors.put(ColorKey.SECONDARY, new Color(0x928374));
        colors.put(ColorKey.ACCENT, new Color(0xcc241d));
        colors.put(ColorKey.WHITE, new Color(0xebdbb2));
        colors.put(ColorKey.BLACK, new Color(0x282828));
        colors.put(ColorKey.RED, new Color(0xcc241d));
        colors.put(ColorKey.ORANGE, new Color(0xd65d0e));
        colors.put(ColorKey.BLUE, new Color(0x458588));
        colors.put(ColorKey.PINK, new Color(0xb16286));
        colors.put(ColorKey.YELLOW, new Color(0xd79921));
        
        // UI element colors
        colors.put(ColorKey.TEXT, new Color(0xebdbb2));
        colors.put(ColorKey.TEXT_SECONDARY, new Color(0x928374));
        colors.put(ColorKey.BORDER, new Color(0x504945));
        colors.put(ColorKey.BUTTON_BG, new Color(0x504945));
        colors.put(ColorKey.BUTTON_FG, new Color(0xebdbb2));
        colors.put(ColorKey.MENU_BG, new Color(0x282828));
        colors.put(ColorKey.MENU_FG, new Color(0xebdbb2));
        
        // Game element colors
        colors.put(ColorKey.WALL, new Color(0x458588));
        colors.put(ColorKey.FLOOR, new Color(0x282828));
        colors.put(ColorKey.PLAYER, new Color(0xd79921));
        colors.put(ColorKey.GHOST, new Color(0xcc241d));
        colors.put(ColorKey.PELLET, new Color(0xebdbb2));
        colors.put(ColorKey.POWER_PELLET, new Color(0x98971a));
    }
}
