package ui.themes;

import java.awt.Color;

/**
 * Default theme implementation using standard Java AWT colors.
 */
public class DefaultTheme extends Theme {
    public DefaultTheme() {
        super("Classic", "Standard Java AWT colors");
    }
    
    @Override
    protected void initializeColors() {
        // Basic Colors
        colors.put(ColorKey.BACKGROUND, Color.WHITE);
        colors.put(ColorKey.FOREGROUND, Color.BLACK);
        colors.put(ColorKey.PRIMARY, new Color(0x0000FF));
        colors.put(ColorKey.SECONDARY, new Color(0x808080));
        colors.put(ColorKey.ACCENT, new Color(0xFF0000));
        colors.put(ColorKey.WHITE, Color.WHITE);
        colors.put(ColorKey.BLACK, Color.BLACK);
        colors.put(ColorKey.RED, new Color(0xFF0000));
        colors.put(ColorKey.ORANGE, new Color(0xFFA500));
        colors.put(ColorKey.BLUE, new Color(0x0000FF));
        colors.put(ColorKey.PINK, new Color(0xFFC0CB));
        colors.put(ColorKey.YELLOW, new Color(0xFFFF00));
        
        // UI element colors
        colors.put(ColorKey.TEXT, Color.BLACK);
        colors.put(ColorKey.TEXT_SECONDARY, new Color(0x404040));
        colors.put(ColorKey.BORDER, new Color(0x808080));
        colors.put(ColorKey.BUTTON_BG, new Color(0xC0C0C0));
        colors.put(ColorKey.BUTTON_FG, Color.BLACK);
        colors.put(ColorKey.MENU_BG, Color.WHITE);
        colors.put(ColorKey.MENU_FG, Color.BLACK);
        
        // Game element colors
        colors.put(ColorKey.WALL, new Color(0x0000FF));
        colors.put(ColorKey.FLOOR, new Color(0xC0C0C0));
        colors.put(ColorKey.PLAYER, new Color(0xFFFF00));
        colors.put(ColorKey.GHOST, new Color(0xFF0000));
        colors.put(ColorKey.PELLET, Color.WHITE);
        colors.put(ColorKey.POWER_PELLET, new Color(0xFFFF00));
    }
}
