package ui.themes;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import config.GameConfig;
import utils.Log;

/**
 * Theme management system for the Pac-Man game.
 * Provides four essential themes: Default, Monokai, Tokyo Night, and Gruvbox.
 * <p>
 * This manager provides:
 * - Four predefined color themes loaded from JSON files
 * - Real-time theme switching
 * - Theme listener notifications
 * - Color palette management for UI elements
 */
public class ThemeManager {
    private static ThemeManager instance;

    /**
     * Available theme types with theme factory suppliers.
     */
    public enum ThemeType {
        DEFAULT(DefaultTheme::new),
        MONOKAI(MonokaiTheme::new),
        TOKYO_NIGHT(TokyoNightTheme::new),
        GRUVBOX(GruvboxTheme::new);

        private final Supplier<Theme> themeSupplier;

        ThemeType(Supplier<Theme> themeSupplier) {
            this.themeSupplier = themeSupplier;
        }

        public Theme createTheme() {
            return themeSupplier.get();
        }
    }

    /**
     * Interface for theme change listeners.
     */
    public interface ThemeListener {
        void onThemeChanged(Theme oldTheme, Theme newTheme);
    }

    // Current theme state
    private Theme currentTheme;
    private List<ThemeListener> listeners;

    /**
     * Private constructor for singleton pattern.
     */
    private ThemeManager() {
        this.listeners = new ArrayList<>();
        applyTheme(GameConfig.DEFAULT_THEME_TYPE);
    }

    /**
     * Private constructor with initial theme.
     */
    private ThemeManager(ThemeType themeType) {
        this.listeners = new ArrayList<>();
        applyTheme(themeType);
    }

    /**
     * Gets the singleton instance of ThemeManager.
     */
    public static ThemeManager getInstance() {
        if (instance == null) {
            instance = new ThemeManager();
        }
        return instance;
    }

    /**
     * Gets the singleton instance with specific initial theme.
     */
    public static ThemeManager getInstance(ThemeType initialTheme) {
        if (instance == null) {
            instance = new ThemeManager(initialTheme);
        }
        return instance;
    }

    /**
     * Applies a theme and notifies listeners.
     */
    public void applyTheme(String themeName) {
        Log.info("Applying theme: " + themeName);
        ThemeType themeType = ThemeType.valueOf(themeName.toUpperCase());
        applyTheme(themeType);
    }

    /**
     * Applies a theme and notifies listeners.
     */
    public void applyTheme(ThemeType themeType) {
        Theme newTheme = themeType.createTheme();
        
        // Compare theme names instead of object references
        if (currentTheme == null || !currentTheme.getName().equals(newTheme.getName())) {
            Theme oldTheme = currentTheme;
            currentTheme = newTheme;
            notifyThemeListeners(oldTheme, newTheme);
        }
    }

    /**
     * Gets the current theme.
     */
    public Theme getCurrentTheme() {
        return currentTheme;
    }

    /**
     * Convenience method to get a color using ColorKey enum.
     */
    public Color getColor(ColorKey key) {
        if (currentTheme == null) {
            throw new IllegalStateException("No theme is currently applied");
        }
        return currentTheme.getColor(key);
    }

    /**
     * Gets all colors from the current theme as ColorKey map.
     */
    public Map<ColorKey, Color> getAllColors() {
        if (currentTheme == null) {
            throw new IllegalStateException("No theme is currently applied");
        }
        return currentTheme.getAllColors();
    }

    /**
     * Gets available themes.
     */
    public ThemeType[] getAvailableThemes() {
        return ThemeType.values();
    }

    /**
     * Adds a theme listener.
     */
    public void addThemeListener(ThemeListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * Removes a theme listener.
     */
    public void removeThemeListener(ThemeListener listener) {
        listeners.remove(listener);
    }

    /**
     * Notifies all theme listeners of theme changes.
     */
    private void notifyThemeListeners(Theme oldTheme, Theme newTheme) {
        for (ThemeListener listener : listeners) {
            try {
                listener.onThemeChanged(oldTheme, newTheme);
            } catch (Exception e) {
                System.err.println("Error notifying theme listener: " + e.getMessage());
            }
        }
    }

    /**
     * Gets theme information as a string.
     */
    public String getThemeInfo() {
        return String.format("Current theme: %s (%s) - %d colors loaded",
                currentTheme.getName(),
                currentTheme.getDescription(),
                currentTheme.getAllColors().size());
    }

    /**
     * Prevents cloning of the singleton instance.
     */
    @Override
    protected Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException("Cannot clone singleton ThemeManager");
    }

}