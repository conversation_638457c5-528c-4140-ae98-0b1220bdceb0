package ui.themes;

import java.util.Arrays;

/**
 * Enum representing color keys for theme management.
 * Each key corresponds to a specific color used in the game UI and graphics.
 * <p>
 * This enum provides:
 * - Centralized color key management
 * - Categorization of color keys for theme application
 * - Support for dynamic color theming
 */
public enum ColorKey {
    // Basic Colors
    BACKGROUND("background", "BASIC"),
    FOREGROUND("foreground", "BASIC"),
    PRIMARY("primary", "BASIC"),
    SECONDARY("secondary", "BASIC"),
    ACCENT("accent", "BASIC"),
    WHITE("white", "BASIC"),
    BLACK("black", "BASIC"),
    RED("red", "BASIC"),
    ORANGE("orange", "BASIC"),
    BLUE("blue", "BASIC"),
    PINK("pink", "BASIC"),
    YELLOW("yellow", "BASIC"),

    // UI elements
    TEXT("text", "UI"),
    TEXT_SECONDARY("text_secondary", "UI"),
    BORDER("border", "UI"),
    BUTTON_BG("button_bg", "UI"),
    BUTTON_FG("button_fg", "UI"),
    MENU_BG("menu_bg", "UI"),
    MENU_FG("menu_fg", "UI"),

    // Game elements
    WALL("wall", "GAME"),
    FLOOR("floor", "GAME"),
    PLAYER("player", "GAME"),
    GHOST("ghost", "GAME"),
    PELLET("pellet", "GAME"),
    POWER_PELLET("power_pellet", "GAME");

    private final String key;
    private final String category;

    ColorKey(String key, String category) {
        this.key = key;
        this.category = category;
    }

    public String getKey() {
        return key;
    }

    public String getCategory() {
        return category;
    }

    // Get all enums in a category
    public static ColorKey[] getByCategory(String category) {
        if (!(category.equals("UI") || category.equals("GAME") || category.equals("BASIC"))) {
            System.err.println("Invalid category: " + category);
        }
        return Arrays.stream(values())
                .filter(c -> c.category.equals(category))
                .toArray(ColorKey[]::new);
    }
}