package menu;

import java.util.List;

import core.GameEngine;
import core.GameState;
import edu.princeton.cs.algs4.StdDraw;
import utils.Log;

public class StartMenu extends Menu {
    private static String title = "Start Menu";
    private GameEngine gameEngine;
    
    public StartMenu(GameEngine gameEngine) {
        this(gameEngine.getWorldWidth(), gameEngine.getWorldHeight(), gameEngine);
    }

    @Override
    public void update(double deltaTime) {
        handleInput();
        // if (menuNeedsRedraw) {
        //     render();
        // menuNeedsRedraw = false; // Reset redraw flag after rendering
        // }
    }

    public StartMenu(int width, int height, GameEngine gameEngine) {
        super(width, height);
        this.gameEngine = gameEngine;
        menuItems = List.of(
            new MenuItem("(N) New Game", "start_new_game"),
            // TODO: change to enabled when save/load implemented
            new MenuItem("(L) Load Game", "load_game", false),
            new MenuItem("(S) Settings", "settings"),
            new MenuItem("(Q) Quit", "quit_game")
        );
    }

    @Override
    public void handleInput() {
        super.handleInput();
        if (!StdDraw.hasNextKeyTyped()) {
            Log.debug("No key pressed in Start Menu.");
            return;
        }
        menuNeedsRedraw = true; // Mark menu for redraw if input is handled
        char key = StdDraw.nextKeyTyped();
        key = Character.toLowerCase(key);
        switch (key) {
            case 'n':
            case 'N':
                System.out.println("New Game selected.");
                // Logic to start a new game
                gameEngine.setState(GameState.SEED_INPUT);
                break;
            case 'l':
            case 'L':
                System.out.println("Load Game selected.");
                // Logic to load a saved game
                break;
            case 'q':
            case 'Q':
                System.out.println("Quit selected.");
                // Logic to quit the game
                break;
            default:
                System.out.println("Invalid key pressed in Start Menu: " + key);
        }
        // Handle user input for the start menu
        System.out.println("Handling input in Start Menu: " + key);
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public void selectCurrentItem() {
        if (menuItems.isEmpty() || selectedIndex < 0 || selectedIndex >= menuItems.size()) {
            return;
        }

        // Get current menu item from menuItems
        MenuItem selectedItem = menuItems.get(selectedIndex);

        // Check if item is enabled
        if (!selectedItem.enabled) {
            return;
        }

        switch (selectedItem.action) {
            case "start_new_game":
                System.out.println("Starting new game...");
                // Logic to start a new game
                break;
            case "load_game":
                System.out.println("Loading game...");
                // Logic to load a saved game
                break;
            case "settings":
                System.out.println("Opening settings...");
                // Logic to open settings menu
                break;
            case "quit_game":
                System.out.println("Quitting game...");
                // Logic to quit the game
                break;
            default:
                System.out.println("Unknown action: " + selectedItem.action);
        }
    }

    @Override
    public void handleBackAction() {
        // Start menu does not have a back action
        Log.warn(title + " does not support back action.");
    }
}