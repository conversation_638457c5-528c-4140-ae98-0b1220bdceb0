package menu;

import java.awt.Color;
import java.awt.Font;
import java.awt.event.KeyEvent;
import java.util.ArrayList;
import java.util.List;

import core.Screen;
import edu.princeton.cs.algs4.StdDraw;
import ui.themes.ColorKey;
import ui.themes.Theme;
import ui.themes.ThemeManager;
import utils.Log;

public abstract class Menu extends Screen {
    public List<MenuItem> menuItems;
    protected boolean menuNeedsRedraw;
    protected Theme colorScheme;

    private Font titleFont;
    private Font menuFont;
    // private Font smallFont;

    protected Color backgroundColor;
    protected Color titleColor;
    protected Color itemsColor;
    protected Color selectedItemColor;

    public int selectedIndex;

    private boolean[] keyPressed = new boolean[256];

    public Menu(int width, int height) {
        super(width, height);
        menuItems = new ArrayList<>();

        this.titleFont = new Font("Arial", Font.BOLD, 36);
        this.menuFont = new Font("Arial", Font.PLAIN, 24);
        // this.smallFont = new Font("Arial", Font.PLAIN, 16);

        refreshColors();
        selectedIndex = 0;
        menuNeedsRedraw = true;
    }
    
    /**
     * Refresh cached colors from current theme
     */
    protected void refreshColors() {
        this.colorScheme = ThemeManager.getInstance().getCurrentTheme();
        backgroundColor = colorScheme.getColor(ColorKey.BACKGROUND);
        titleColor = colorScheme.getColor(ColorKey.PRIMARY);
        itemsColor = colorScheme.getColor(ColorKey.SECONDARY);
        selectedItemColor = colorScheme.getColor(ColorKey.ACCENT);
    }
    
    @Override
    public void onThemeChanged(Theme oldTheme, Theme newTheme) {
        Log.debug("Menu updating colors for theme change: " + oldTheme.getName() + " -> " + newTheme.getName());
        refreshColors();
        menuNeedsRedraw = true; // Force redraw with new colors
    }

    // @Override
    // public abstract void render();

    // Please note that abstract menu handle input method only treats navigation
    // operations.
    // you should call concrete menu's handleInput method to handle specific
    // actions.
    @Override
    public void handleInput() {
        boolean hasInput = false;

        if (StdDraw.isKeyPressed(KeyEvent.VK_UP) && !keyPressed[KeyEvent.VK_UP]) {
            moveSelectionUp();
            keyPressed[KeyEvent.VK_UP] = true;
            hasInput = true;
            Log.debug("DEBUG: UP key pressed");
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_UP)) {
            keyPressed[KeyEvent.VK_UP] = false;
        }

        if (StdDraw.isKeyPressed(KeyEvent.VK_DOWN) && !keyPressed[KeyEvent.VK_DOWN]) {
            moveSelectionDown();
            keyPressed[KeyEvent.VK_DOWN] = true;
            hasInput = true;
            Log.debug("DEBUG: DOWN key pressed");
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_DOWN)) {
            keyPressed[KeyEvent.VK_DOWN] = false;
        }

        if (StdDraw.isKeyPressed(KeyEvent.VK_ENTER) && !keyPressed[KeyEvent.VK_ENTER]) {
            selectCurrentItem();
            keyPressed[KeyEvent.VK_ENTER] = true;
            hasInput = true;
            Log.debug("DEBUG: ENTER key pressed");
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_ENTER)) {
            keyPressed[KeyEvent.VK_ENTER] = false;
        }

        if (StdDraw.isKeyPressed(KeyEvent.VK_SPACE) && !keyPressed[KeyEvent.VK_SPACE]) {
            selectCurrentItem();
            keyPressed[KeyEvent.VK_SPACE] = true;
            hasInput = true;
            Log.debug("DEBUG: SPACE key pressed");
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_SPACE)) {
            keyPressed[KeyEvent.VK_SPACE] = false;
        }

        if (StdDraw.isKeyPressed(KeyEvent.VK_ESCAPE) && !keyPressed[KeyEvent.VK_ESCAPE]) {
            handleBackAction();
            keyPressed[KeyEvent.VK_ESCAPE] = true;
            hasInput = true;
            Log.debug("DEBUG: ESCAPE key pressed");
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_ESCAPE)) {
            keyPressed[KeyEvent.VK_ESCAPE] = false;
        }

        if (hasInput) {
            menuNeedsRedraw = true; // Only redraw if input was handled
        }
    }

    private void moveSelectionUp() {
        if (menuItems.isEmpty()) {
            return;
        }

        int originalIndex = selectedIndex;

        // Decrease selectedIndex
        selectedIndex--;

        // Wrap around to bottom if at top
        if (selectedIndex < 0) {
            selectedIndex = menuItems.size() - 1;
        }

        // Skip disabled menu items
        int attempts = 0;
        while (!menuItems.get(selectedIndex).enabled && attempts < menuItems.size()) {
            selectedIndex--;
            if (selectedIndex < 0) {
                selectedIndex = menuItems.size() - 1;
            }
            attempts++;
        }

        // If all items are disabled, revert to original
        if (attempts >= menuItems.size()) {
            selectedIndex = originalIndex;
        }
    }

    private void moveSelectionDown() {
        if (menuItems.isEmpty()) {
            return;
        }

        int originalIndex = selectedIndex;

        // Increase selectedIndex
        selectedIndex++;

        // Wrap around to top if at bottom
        if (selectedIndex >= menuItems.size()) {
            selectedIndex = 0;
        }

        // Skip disabled menu items
        int attempts = 0;
        while (!menuItems.get(selectedIndex).enabled && attempts < menuItems.size()) {
            selectedIndex++;
            if (selectedIndex >= menuItems.size()) {
                selectedIndex = 0;
            }
            attempts++;
        }

        // If all items are disabled, revert to original
        if (attempts >= menuItems.size()) {
            selectedIndex = originalIndex;
        }
    }

    public abstract void selectCurrentItem();

    public abstract void handleBackAction();

    public abstract String getTitle();

    /**
     * Renders the current menu to the screen.
     */
    @Override
    public void render() {
        Log.debug("DEBUG: renderMenu() called, menuNeedsRedraw=" + menuNeedsRedraw);
        if (!menuNeedsRedraw) {
            return; // Skip rendering if menu hasn't changed
        }

        // Render background
        StdDraw.clear(backgroundColor);

        // TODO: we may need to draw semi-transparent background overlay for pause menu.

        // Render title
        String title = getTitle();
        // Set title font and color
        StdDraw.setFont(titleFont);
        StdDraw.setPenColor(titleColor);
        // Draw title centered at top of screen using world coordinates
        double titleX = width * 0.5; // Center horizontally
        double titleY = height * 0.85; // Near top
        StdDraw.text(titleX, titleY, title);

        // Render menu items
        StdDraw.setFont(menuFont);
        double startY = height * 0.6; // Start below title using world coordinates
        for (int i = 0; i < menuItems.size(); i++) {
            MenuItem item = menuItems.get(i);

            // Calculate item position using world coordinates
            double itemSpacing = height * 0.08; // 8% of world height spacing between items
            double y = startY - (i * itemSpacing);
            double centerX = width * 0.5; // Center horizontally
            double arrowX = width * 0.3; // Arrow position

            // Set color based on selection and enabled state
            Color itemColor;
            if (i == selectedIndex && item.enabled) {
                itemColor = selectedItemColor; // Highlighted
            } else if (!item.enabled) {
                itemColor = Color.GRAY; // Disabled
            } else {
                itemColor = itemsColor; // Normal
            }

            StdDraw.setPenColor(itemColor);

            // Draw item text
            StdDraw.text(centerX, y, item.text);

            // Add selection indicator (arrow, highlight box, etc.)
            if (i == selectedIndex && item.enabled) {
                // Draw selection arrow
                StdDraw.text(arrowX, y, ">");
            }
        }

        StdDraw.show();
        Log.debug("DEBUG: Menu rendered successfully.");

        menuNeedsRedraw = false; // Mark as clean after rendering
    }

    /**
     * Menu item class for menu entries.
     */
    public static class MenuItem {
        public final String text;
        public final String action;
        public final boolean enabled;

        public MenuItem(String text, String action, boolean enabled) {
            this.text = text;
            this.action = action;
            this.enabled = enabled;
        }

        public MenuItem(String text, String action) {
            this(text, action, true);
        }
    }
}