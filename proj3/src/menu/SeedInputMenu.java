package menu;

import config.GameConfig;
import core.GameEngine;
import core.GameState;
import edu.princeton.cs.algs4.StdDraw;
import ui.themes.ColorKey;
import utils.Log;

public class SeedInputMenu extends Menu {
    private String title;
    private long seed; // Placeholder for seed input
    private StringBuilder seedInput = new StringBuilder();
    private GameEngine gameEngine;

    public SeedInputMenu(GameEngine gameEngine) {
        super(gameEngine.getWorldWidth(), gameEngine.getWorldHeight());
        this.gameEngine = gameEngine;
        this.title = "Enter Seed";
        this.seed = gameEngine.getCurrentSeed();
        // Pre-fill the input with the current seed so user can see and modify it
        this.seedInput = new StringBuilder(String.valueOf(this.seed));
    }

    @Override
    public void handleInput() {
        // Seed input handling is a bit different from other menus.
        // User can only input numerics and backspace.
        if (!StdDraw.hasNextKeyTyped()) {
            Log.debug("No key pressed in Seed Input Menu.");
            return;
        }

        char key = StdDraw.nextKeyTyped();
        key = Character.toLowerCase(key);

        if (Character.isDigit(key)) {
            seedInput.append(key);
        } else if (key == 's') {
            // Start game with entered seed
            if (seedInput.length() > 0) {
                try {
                    seed = Long.parseLong(seedInput.toString());
                    gameEngine.setSeed(seed);
                    gameEngine.setState(GameState.HUB_WORLD);
                    Log.info("Starting game with seed: " + seed);
                } catch (NumberFormatException e) {
                    Log.error("Invalid seed input: " + seedInput);
                }
            } else {
                // Use default seed if input is empty
                seed = GameConfig.DEFAULT_SEED;
                gameEngine.setSeed(seed);
                gameEngine.setState(GameState.HUB_WORLD);
                Log.info("Starting game with default seed: " + seed);
            }
        } else if (key == '\u0008') { // Backspace
            if (seedInput.length() > 0) {
                seedInput.deleteCharAt(seedInput.length() - 1);
            }
        }
    }

    @Override
    public void render() {
        // TODO: make the seed input menu look nice
        /*
         * 
         * // Clear screen with background color
        StdDraw.clear(java.awt.Color.BLACK);
        
        // Set text color and font
        StdDraw.setPenColor(java.awt.Color.WHITE);
        StdDraw.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 36));
        
        // Title
        double titleX = worldWidth * 0.5;
        double titleY = worldHeight * 0.85;
        StdDraw.text(titleX, titleY, "Enter Seed");
        
        // Instructions
        StdDraw.setFont(new java.awt.Font("Arial", java.awt.Font.PLAIN, 24));
        double instructX = worldWidth * 0.5;
        double instructY = worldHeight * 0.7;
        StdDraw.text(instructX, instructY, "Type digits (0-9) to enter seed");
        
        double instructY2 = worldHeight * 0.65;
        StdDraw.text(instructX, instructY2, "Press S to start game");
        
        // Current seed input display
        StdDraw.setFont(new java.awt.Font("Arial", java.awt.Font.BOLD, 32));
        StdDraw.setPenColor(java.awt.Color.YELLOW);
        double seedX = worldWidth * 0.5;
        double seedY = worldHeight * 0.45;
        String seedDisplay = seedInput.length() > 0 ? seedInput.toString() : "_";
        StdDraw.text(seedX, seedY, "Seed: " + seedDisplay);
        
        // Additional info
        StdDraw.setPenColor(java.awt.Color.LIGHT_GRAY);
        StdDraw.setFont(new java.awt.Font("Arial", java.awt.Font.PLAIN, 16));
        double infoX = worldWidth * 0.5;
        double infoY = worldHeight * 0.25;
        StdDraw.text(infoX, infoY, "Leave empty for random seed");
         */
        StdDraw.clear(colorScheme.getColor(ColorKey.BACKGROUND));
        StdDraw.setPenColor(colorScheme.getColor(ColorKey.FOREGROUND));

        // Title
        StdDraw.text(width / 2, height - 1, title);

        // Instructions
        StdDraw.text(width / 2, height - 3, "Current seed is shown below. You can modify it.");
        StdDraw.text(width / 2, height - 4, "Type digits (0-9) to edit, Backspace to delete");
        StdDraw.text(width / 2, height - 5, "Press 'S' to start game with this seed");

        // Current seed display
        String seedDisplay = seedInput.length() > 0 ? seedInput.toString() : "0";
        StdDraw.text(width / 2, height / 2, "Seed: " + seedDisplay);

        StdDraw.show();
    }

    @Override
    public String getTitle() {
        return title;
    }

    @Override
    public void selectCurrentItem() {
        Log.warn(title + " does not support item selection.");
    }

    @Override
    public void handleBackAction() {
        Log.info("Returning to start menu from seed input menu.");
        this.gameEngine.setState(GameState.START_MENU);
    }

    @Override
    public void update(double deltaTime) {
        handleInput();
    }

    /**
     * Refresh the seed input to show the current seed from GameEngine
     * This should be called when the menu becomes active
     */
    public void refreshSeedInput() {
        this.seed = gameEngine.getCurrentSeed();
        this.seedInput = new StringBuilder(String.valueOf(this.seed));
        Log.info("Seed input refreshed with current seed: " + this.seed);
    }
}