package menu;

import java.awt.event.KeyEvent;
import java.util.List;

import core.GameEngine;
import core.GameState;
import edu.princeton.cs.algs4.StdDraw;
import ui.themes.ThemeManager;
import ui.themes.ThemeManager.ThemeType;
import utils.Log;

public class SettingsMenu extends Menu {
    private static String title = "Settings";
    private GameEngine gameEngine;
    private ThemeManager themeManager;
    private int currentThemeIndex;
    private ThemeType[] availableThemes;
    
    public SettingsMenu(GameEngine gameEngine) {
        this(gameEngine.getWorldWidth(), gameEngine.getWorldHeight(), gameEngine);
    }

    public SettingsMenu(int width, int height, GameEngine gameEngine) {
        super(width, height);
        this.gameEngine = gameEngine;
        this.themeManager = ThemeManager.getInstance();
        this.availableThemes = themeManager.getAvailableThemes();
        
        findCurrentThemeIndex();
        updateMenuItems();
    }
    
    private void findCurrentThemeIndex() {
        String currentThemeName = themeManager.getCurrentTheme().getName();
        for (int i = 0; i < availableThemes.length; i++) {
            if (availableThemes[i].createTheme().getName().equals(currentThemeName)) {
                currentThemeIndex = i;
                break;
            }
        }
    }
    
    private void updateMenuItems() {
        String themeDisplayName = getThemeDisplayName(availableThemes[currentThemeIndex]);
        menuItems = List.of(
            new MenuItem("Theme: " + themeDisplayName, "theme"),
            new MenuItem("(ESC) Back", "back"),
            new MenuItem("(t/T) show/hide ghost paths", "toggle_ghost_paths", false),
            new MenuItem("(h/H) show/hide hover text", "toggle_hover_text", false)
        );
        menuNeedsRedraw = true;
    }
    
    private String getThemeDisplayName(ThemeType themeType) {
        switch (themeType) {
            case DEFAULT: return "Default";
            case MONOKAI: return "Monokai";
            case TOKYO_NIGHT: return "Tokyo Night";
            case GRUVBOX: return "Gruvbox";
            default: return themeType.name();
        }
    }
    

    @Override
    public void update(double deltaTime) {
        handleInput();
    }

    @Override
    public void handleInput() {
        super.handleInput();
        
        boolean hasInput = false;
        
        if (StdDraw.isKeyPressed(KeyEvent.VK_LEFT) && !keyPressed[KeyEvent.VK_LEFT]) {
            if (selectedIndex == 0) {
                changeTheme(-1);
                hasInput = true;
            }
            keyPressed[KeyEvent.VK_LEFT] = true;
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_LEFT)) {
            keyPressed[KeyEvent.VK_LEFT] = false;
        }
        
        if (StdDraw.isKeyPressed(KeyEvent.VK_RIGHT) && !keyPressed[KeyEvent.VK_RIGHT]) {
            if (selectedIndex == 0) {
                changeTheme(1);
                hasInput = true;
            }
            keyPressed[KeyEvent.VK_RIGHT] = true;
        } else if (!StdDraw.isKeyPressed(KeyEvent.VK_RIGHT)) {
            keyPressed[KeyEvent.VK_RIGHT] = false;
        }
        
        if (hasInput) {
            menuNeedsRedraw = true;
        }
        
        if (!StdDraw.hasNextKeyTyped()) {
            return;
        }
        
        char key = StdDraw.nextKeyTyped();
        key = Character.toLowerCase(key);
        
        switch (key) {
            default:
                Log.debug("Key pressed in Settings Menu: " + key);
        }
    }
    
    private boolean[] keyPressed = new boolean[256];
    
    private void changeTheme(int direction) {
        currentThemeIndex += direction;
        
        if (currentThemeIndex < 0) {
            currentThemeIndex = availableThemes.length - 1;
        } else if (currentThemeIndex >= availableThemes.length) {
            currentThemeIndex = 0;
        }
        
        ThemeType newTheme = availableThemes[currentThemeIndex];
        themeManager.applyTheme(newTheme);
        
        // Colors will be automatically updated via onThemeChanged() listener
        // Just need to update the menu items to show new theme name
        updateMenuItems();
        
        Log.info("Changed theme to: " + getThemeDisplayName(newTheme));
    }

    @Override
    public void selectCurrentItem() {
        if (menuItems.isEmpty() || selectedIndex < 0 || selectedIndex >= menuItems.size()) {
            return;
        }

        MenuItem selectedItem = menuItems.get(selectedIndex);

        if (!selectedItem.enabled) {
            return;
        }

        switch (selectedItem.action) {
            case "theme":
                changeTheme(1);
                break;
            case "back":
                goBack();
                break;
            default:
                Log.warn("Unknown action: " + selectedItem.action);
        }
    }

    @Override
    public void handleBackAction() {
        goBack();
    }
    
    @Override
    public String getTitle() {
        return title;
    }
    
    private void goBack() {
        Log.info("Going back from settings");
        // GameEngine will automatically determine the previous screen using the stack
        gameEngine.setState(GameState.PAUSED);
    }
}