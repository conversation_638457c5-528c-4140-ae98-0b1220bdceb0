package menu;

import java.util.List;

import core.GameEngine;
import core.GameState;
import edu.princeton.cs.algs4.StdDraw;
import utils.Log;

public class PauseMenu extends Menu {
    private static String title = "Game Paused";
    private GameEngine gameEngine;
    
    public PauseMenu(GameEngine gameEngine) {
        this(gameEngine.getWorldWidth(), gameEngine.getWorldHeight(), gameEngine);
    }

    public PauseMenu(int width, int height, GameEngine gameEngine) {
        super(width, height);
        this.gameEngine = gameEngine;
        
        menuItems = List.of(
            new MenuItem("(R) Resume", "resume"),
            new MenuItem("(Q) Quit", "quit"),
            new MenuItem("(S) Settings", "settings")
        );
    }

    @Override
    public void update(double deltaTime) {
        handleInput();
    }

    @Override
    public void handleInput() {
        super.handleInput(); // Handle navigation keys
        
        if (!StdDraw.hasNextKeyTyped()) {
            return;
        }
        
        menuNeedsRedraw = true;
        char key = StdDraw.nextKeyTyped();
        key = Character.toLowerCase(key);
        
        switch (key) {
            case 'r':
            case 'R':
                resumeGame();
                break;
            case 'q':
            case 'Q':
                quitGame();
                break;
            case 's':
            case 'S':
                openSettings();
                break;
            default:
                Log.debug("Invalid key pressed in Pause Menu: " + key);
        }
    }

    @Override
    public void selectCurrentItem() {
        if (menuItems.isEmpty() || selectedIndex < 0 || selectedIndex >= menuItems.size()) {
            return;
        }

        MenuItem selectedItem = menuItems.get(selectedIndex);

        if (!selectedItem.enabled) {
            return;
        }

        switch (selectedItem.action) {
            case "resume":
                resumeGame();
                break;
            case "quit":
                quitGame();
                break;
            case "settings":
                openSettings();
                break;
            default:
                Log.warn("Unknown action: " + selectedItem.action);
        }
    }

    @Override
    public void handleBackAction() {
        // ESC key should resume the game
        resumeGame();
    }
    
    @Override
    public String getTitle() {
        return title;
    }
    
    private void resumeGame() {
        Log.info("Resuming game");
        // For now, explicitly set to HUB_WORLD - GameEngine will handle the stack logic
        gameEngine.setState(GameState.HUB_WORLD);
    }
    
    private void quitGame() {
        Log.info("Quitting to main menu");
        gameEngine.setState(GameState.START_MENU);
    }
    
    private void openSettings() {
        Log.info("Opening settings menu from pause");
        gameEngine.setState(GameState.SETTINGS);
    }
}